import { createSlice } from '@reduxjs/toolkit';

import { TABLE } from '~/enums';

// Helper functions for localStorage
const getStoredValue = (key, defaultValue) => {
  try {
    const stored = localStorage.getItem(key);
    return stored !== null ? JSON.parse(stored) : defaultValue;
  } catch {
    return defaultValue;
  }
};

const setStoredValue = (key, value) => {
  try {
    localStorage.setItem(key, JSON.stringify(value));
  } catch {
    // Silently fail if localStorage is not available
  }
};

const initialState = {
  selectedView: TABLE,
  structuralPositions: [],
  chosenCompanyIds: null,
  chosenCompanyId: null,
  chosenTopCurrencyAccountId: null,
  dateRange: { startDate: null, endDate: null },
  participantsTrails: null,
  structuralThreshold: getStoredValue('structuralPositions_durationThreshold', 9),
  materialityThreshold: getStoredValue('structuralPositions_materialityThreshold', null),
};

// Slice
export const cashPoolStructuralPositionsSlice = createSlice({
  name: 'cashPoolStructuralPositions',
  initialState,
  reducers: {
    resetStructuralPositionsData: () => {
      return { ...initialState };
    },
    updateField: (state, action) => {
      const newState = { ...state, ...action.payload };

      // Save threshold values to localStorage when they change
      if (action.payload.structuralThreshold !== undefined) {
        setStoredValue('structuralPositions_durationThreshold', action.payload.structuralThreshold);
      }
      if (action.payload.materialityThreshold !== undefined) {
        setStoredValue('structuralPositions_materialityThreshold', action.payload.materialityThreshold);
      }

      return newState;
    },
    updateDateRange: (state, action) => {
      const { startDate, endDate } = action.payload;
      state.dateRange.startDate = startDate ?? undefined;
      state.dateRange.endDate = endDate ?? undefined;
    },
    setDefaultChosenCompanies: (state, action) => {
      const { chosenCompanyIds } = action.payload;
      return { ...state, chosenCompanyIds: chosenCompanyIds, chosenCompanyId: chosenCompanyIds[0] };
    },
  },
});

export default cashPoolStructuralPositionsSlice.reducer;

// Actions
export const { resetStructuralPositionsData, updateField, updateDateRange, setDefaultChosenCompanies } =
  cashPoolStructuralPositionsSlice.actions;

// Selectors
export const cashPoolStructuralPositionsSelector = (state) => state.cashPoolStructuralPositions;
