import { SingleSelect } from 'ui';
import { getOptionsFromArray } from 'utils/arrays';
import type { SingleSelectWidthType, DateFormatType } from 'types';

const dateFormats = ['YYYY-MM-DD', 'DD-MM-YYYY', 'MM-DD-YYYY'];
const options = getOptionsFromArray(dateFormats);

type DateFormatSingleSelectProps = {
  label?: string;
  width?: SingleSelectWidthType;
  value: DateFormatType;
  onChange: Function;
};

const DateFormatSingleSelect = ({
  label = 'Date format',
  value,
  width = 'm',
  onChange,
}: DateFormatSingleSelectProps) => (
  <SingleSelect label={label} options={options} value={value} width={width} onChange={onChange} />
);

export default DateFormatSingleSelect;
