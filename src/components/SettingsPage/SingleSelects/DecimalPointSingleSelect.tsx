import { COMMA, DOT } from 'enums';
import { SingleSelect } from 'ui';
import type { SingleSelectWidthType, DecimalPointType } from 'types';

const options = [
  { label: 'period', value: DOT },
  { label: 'comma', value: COMMA },
];

type DecimalPointSingleSelectProps = {
  label?: string;
  width?: SingleSelectWidthType;
  value: DecimalPointType;
  onChange: Function;
};

const DecimalPointSingleSelect = ({
  label = 'Decimal Separator',
  value,
  width = 'm',
  onChange,
}: DecimalPointSingleSelectProps) => (
  <SingleSelect label={label} options={options} value={value} width={width} onChange={onChange} />
);

export default DecimalPointSingleSelect;
