import { useContext, useEffect, useState } from 'react';
import { useHistory } from 'react-router-dom';

import { updateClient } from 'api';
import { UserInfoContext } from 'context/user';
import { isAdmin } from 'enums';
import { errorHandler } from 'utils/errors';
import { useUnsavedChangesWarning } from 'hooks';
import { Box, Button, Card, FlexLayout, Text, RadioGroup } from 'ui';
import { showToast } from 'ui/components/Toast';

const ClientSettings = () => {
  const { userInfo, setUserInfo } = useContext(UserInfoContext);
  const [isLoanApproachCalculated, setIsLoanApproachCalculated] = useState(false);
  const [isCreditRatingAnonymized, setIsCreditRatingAnonymized] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const history = useHistory();
  const [Prompt] = useUnsavedChangesWarning({ isDirty });

  const onSave = async () => {
    try {
      const client = await updateClient({ isLoanApproachCalculated, isCreditRatingAnonymized });
      setUserInfo({ ...userInfo, client: { ...userInfo.client, ...client } });
      showToast('Successfully updated.');
      setIsDirty(false);
    } catch (error) {
      errorHandler(error);
    }
  };

  const onInputChange = (setter: React.Dispatch<React.SetStateAction<boolean>>) => (value: boolean) => {
    setIsDirty(true);
    setter(value);
  };

  useEffect(() => {
    setIsLoanApproachCalculated(userInfo.client.isLoanApproachCalculated);
    setIsCreditRatingAnonymized(userInfo.client.isCreditRatingAnonymized);
  }, [userInfo]);

  return (
    <>
      <Card p={6} space={4}>
        <Text variant="2l-spaced" color="deep-sapphire">
          Loans
        </Text>
        <Box
          sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(3, 1fr)' }}
          disabled={!isAdmin(userInfo.role)}
        >
          <RadioGroup
            label="Lender perspective"
            options={[
              { label: 'On', value: true },
              { label: 'Off', value: false },
            ]}
            tooltip="
              When the lender perspective is enabled, the software evaluates the benchmark<br /> 
              from both the lender's and the borrower's viewpoints, applying whichever entity<br />
              has the higher loan cost. <br /><br />
              This ensures the lender does not lend below their own cost. When the <br />
              lender perspective is disabled, only the borrower's viewpoint is considered,<br />
              and its loan cost is applied in the benchmarking analysis."
            width="fullWidth"
            value={isLoanApproachCalculated}
            onChange={onInputChange(setIsLoanApproachCalculated)}
          />
        </Box>
      </Card>
      <Card p={6} space={4}>
        <Text variant="2l-spaced" color="deep-sapphire">
          Credit ratings
        </Text>
        <Box
          sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(3, 1fr)' }}
          disabled={!isAdmin(userInfo.role)}
        >
          <RadioGroup
            label="Anonymize credit ratings"
            options={[
              { label: 'On', value: true },
              { label: 'Off', value: false },
            ]}
            tooltip="
              Set the default state for anonymizing credit ratings.<br /> 
              This can still be adjusted manually in the Credit Rating section."
            width="fullWidth"
            value={isCreditRatingAnonymized}
            onChange={onInputChange(setIsCreditRatingAnonymized)}
          />
        </Box>
      </Card>

      <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="space-between">
        <Button text="Back" variant="gray" onClick={history.goBack} />
        <Button text="Save" disabled={!isAdmin(userInfo.role)} onClick={onSave} />
      </FlexLayout>
      {Prompt}
    </>
  );
};

export default ClientSettings;
