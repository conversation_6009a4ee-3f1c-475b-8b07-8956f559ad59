import { assessments } from '~/components/Shared/AssessmentGraph';
import { creditRatings } from '~/components/Shared/CreditRatingSingleSelect';
import { industries } from '~/components/Shared/IndustrySingleSelect';
import { Box, FlexLayout, Text } from '~/ui';
import { getCompanyAssessment, getCompanyRating, getCompanyRatingAdj } from '~/utils/companies';

export function getCompaniesData(data = [], countriesByName) {
  return data.map((company) => {
    const { country, id, industry, name, parentCompanyId } = company;

    return {
      id,
      parentCompanyId,
      country: `${countriesByName[country]?.flagEmoji} ${country}`,
      industry,
      name,
      rating: getCompanyRating(company),
      assessmentName: getCompanyAssessment(company),
      ratingAdj: getCompanyRatingAdj(company),
    };
  });
}

export function getCompaniesColumns() {
  return [
    {
      label: 'Company',
      sortBy: 'name',
      value: 'name',
      width: 250,
      wrapText: true,
      renderCustomCell: (item) => {
        const { id, name, parentCompanyId } = item;
        const isParentCompany = id === parentCompanyId;

        return (
          <FlexLayout alignItems="center" space={2}>
            {isParentCompany && (
              <Box bg="shakespeare" sx={{ height: '8px', flexShrink: '0', width: '8px', borderRadius: 'round' }} />
            )}
            <Text color="deep-sapphire" variant="m-spaced">
              {name}
            </Text>
          </FlexLayout>
        );
      },
    },
    { label: 'Country', sortBy: 'country', value: 'country' },
    { label: 'Sector', sortBy: 'industry', value: 'industry' },
    { label: 'Rating', sortArray: creditRatings, sortBy: 'rating', sortType: 'array', value: 'rating' },
    {
      label: 'Assessment',
      sortArray: assessments,
      sortBy: 'assessmentName',
      sortType: 'array',
      value: 'assessmentName',
    },
    { label: 'Rating (ADJ.)', sortBy: 'ratingAdj', value: 'ratingAdj' },
  ];
}

const getIsAnyImplicitQuestionAnswered = (row) => {
  for (let i = 1; i <= 11; i++) {
    if (row[`Implicit Support Q #${i}`] === 'X') {
      return true;
    }
  }
  return false;
};

export async function getCompaniesFromSheet(sheet, countriesByName) {
  if (sheet.length === 0) {
    throw new Error('Sheet is empty. Please upload populated sheet.');
  }

  const companies = [];
  for (let i = 0, len = sheet.length; i < len; i++) {
    const company = sheet[i];

    const name = company['Company Name*'];
    const industry = company['Sector*'];
    const country = company['Country*'];
    const rating = company['Issuer Rating'] ?? null;
    const probabilityOfDefault = company['Probability of Default'] ?? null;
    const parentCompanyName = company['Parent Company Name'] ?? null;

    if (typeof name === 'number') throw new Error('Company Name cannot be a number.');
    if (typeof parentCompanyName === 'number') throw new Error('Parent Company Name cannot be a number.');

    const ringFencing = company['Implicit Support Q #1'] === 'X';
    const question1 = company['Implicit Support Q #2'] === 'X';
    const question2 = company['Implicit Support Q #3'] === 'X';
    const question3 = company['Implicit Support Q #4'] === 'X';
    const question4 = company['Implicit Support Q #5'] === 'X';
    const question5 = company['Implicit Support Q #6'] === 'X';
    const question6 = company['Implicit Support Q #7'] === 'X';
    const question7 = company['Implicit Support Q #8'] === 'X';
    const question8 = company['Implicit Support Q #9'] === 'X';
    const question9 = company['Implicit Support Q #10'] === 'X';
    const question10 = company['Implicit Support Q #11'] === 'X';

    const isAnyImplicitQuestionAnswered = getIsAnyImplicitQuestionAnswered(company);

    if (!name?.trim() && !industry?.trim() && !country?.trim() && !rating?.trim() && !probabilityOfDefault?.trim()) {
      continue;
    }

    if (!name && !industry && !country) {
      throw new Error('Company name, sector and country are required fields.');
    }
    if (name == null) {
      throw new Error('Company name cannot be empty.');
    } else if (name.length > 255) {
      throw new Error(`Company: ${name} too long. Company name should have less then 255 characters.`);
    }

    if (industry == null) {
      throw new Error('Company industry cannot be empty.');
    } else if (!industries.includes(industry)) {
      throw new Error(`Industry ${industry} is invalid.`);
    }

    if (country == null) {
      throw new Error('Company country cannot be empty.');
    } else if (!countriesByName[country]) {
      throw new Error(`Country ${country} is invalid.`);
    }

    if (rating && !creditRatings.includes(rating)) {
      throw new Error(`Issuer rating ${rating} is invalid.`);
    }

    if (probabilityOfDefault != null && (probabilityOfDefault < 0 || probabilityOfDefault > 100)) {
      throw new Error(`Probability of default ${probabilityOfDefault} needs to be number in range 0-100.`);
    }

    companies.push({
      parentCompanyName,
      name,
      industry,
      country,
      creditRating: {
        rating,
        ratingAdj: null,
        probabilityOfDefault,
        probabilityOfDefaultAdj: null,
      },
      assessment: isAnyImplicitQuestionAnswered
        ? {
            answers: {
              ringFencing,
              question1,
              question2,
              question3,
              question4,
              question5,
              question6,
              question7,
              question8,
              question9,
              question10,
            },
          }
        : null,
    });
  }

  return companies;
}
