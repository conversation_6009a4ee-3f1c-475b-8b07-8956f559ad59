import { isAfter } from 'date-fns';

import { reportStatusColors, lenderOrGuarantorTypeEnum } from '~/enums';
import { creditRatings } from '~/components/Shared/CreditRatingSingleSelect';
import { seniorities } from '~/components/Shared/SenioritySingleSelect';
import { Text } from '~/ui';
import { getCompanyRating, getCompanyRatingAdj } from '~/utils/companies';
import { createTenor, formatDateString } from '~/utils/dates';
import { getSheetData } from '~/utils/excel';
import { getReportUnit } from '~/utils/report';
import { capitalize, displayNumber2, getNote, getPricingApproach } from '~/utils/strings';
import { getVisibleColumns } from '~/utils/tables';
import { checkClientFeatureFlags } from '~/utils/clientFeatureFlags';
import { Client } from '~/types';

function getLoanData(loan, userInfo) {
  const {
    amount,
    borrower,
    currency,
    editable,
    id,
    isPortfolio,
    issueDate,
    lender,
    maturityDate,
    paymentFrequency,
    rateType,
    pricingApproach,
    report,
    seniority,
    status,
    type,
    isThirdParty,
    dayCount,
    createdAt,
    updatedAt,
    deletedAt,
    note,
  } = loan;
  const statusPossibleExpire = isAfter(new Date(), new Date(maturityDate)) ? 'Expired' : status;
  const maxDig = rateType?.type === 'float' ? 0 : 2;
  const numberDisplayOptions = { decimalPoint: userInfo.decimalPoint, defaultValue: '-', maxDig, minDig: 0 };

  return {
    createdAt: formatDateString(createdAt, userInfo.dateFormat),
    updatedAt: formatDateString(updatedAt, userInfo.dateFormat),
    deletedAt: formatDateString(deletedAt, userInfo.dateFormat) || 'n/a',
    id,
    lenderName: lender?.name,
    borrowerName: borrower?.name,
    currency: currency,
    amount: displayNumber2(amount, numberDisplayOptions),
    principalRepayment: 'At Maturity',
    rateType: capitalize(rateType?.type),
    loanType: type,
    dayCount,
    paymentFrequency,
    borrowerRatingStandalone: getCompanyRating(borrower),
    borrowerRatingImplicitAdj: getCompanyRatingAdj(borrower),
    tenor: displayNumber2(createTenor(issueDate, maturityDate).toFixed(2), numberDisplayOptions),
    isPortfolio,
    issueDate: formatDateString(issueDate, userInfo.dateFormat),
    maturityDate: formatDateString(maturityDate, userInfo.dateFormat),
    editable,
    status: statusPossibleExpire,
    seniority,
    finalInterestRate: displayNumber2(report.finalInterestRate, numberDisplayOptions) + getReportUnit(rateType, true),
    report,
    lowerBound: displayNumber2(report.lowerBound, numberDisplayOptions) + getReportUnit(rateType, true),
    midPoint: displayNumber2(report.midPoint, numberDisplayOptions) + getReportUnit(rateType, true),
    upperBound: displayNumber2(report.upperBound, numberDisplayOptions) + getReportUnit(rateType, true),
    lenderType: isThirdParty ? lenderOrGuarantorTypeEnum.THIRD_PARTY : lenderOrGuarantorTypeEnum.INTERCOMPANY,
    note: getNote(note),
    pricingApproach: getPricingApproach(pricingApproach),
    uniqueId: checkClientFeatureFlags(Client.MARS, userInfo.client.name) ? loan.externalId : id,
  };
}

export function getLoansData(data = [], userInfo) {
  return data.map((item) => getLoanData(item, userInfo));
}

const columns = [
  { label: 'Lender', sortBy: 'lenderName', value: 'lenderName', width: 250, wrapText: true },
  { label: 'Borrower', sortBy: 'borrowerName', value: 'borrowerName', width: 250, wrapText: true },
  { label: 'Currency', sortBy: 'currency', value: 'currency' },
  { label: 'Amount', sortBy: 'amount', value: 'amount' },
  { label: 'Principal repayment', sortBy: 'principalRepayment', value: 'principalRepayment' },
  { label: 'Rate type', sortBy: 'rateType', value: 'rateType' },
  { label: 'Loan type', sortBy: 'loanType', value: 'loanType' },
  { label: 'Day count', sortBy: 'dayCount', value: 'dayCount' },
  { label: 'Interest compounding frequency', sortBy: 'paymentFrequency', value: 'paymentFrequency' },
  {
    label: 'Borrower rating standalone',
    sortArray: creditRatings,
    sortBy: 'borrowerRatingStandalone',
    sortType: 'array',
    value: 'borrowerRatingStandalone',
  },
  {
    label: 'Borrower rating implicit adj.',
    sortArray: creditRatings,
    sortBy: 'borrowerRatingImplicitAdj',
    sortType: 'array',
    value: 'borrowerRatingImplicitAdj',
  },
  { label: 'Issue date', sortBy: 'issueDate', value: 'issueDate' },
  { label: 'Maturity date', sortBy: 'maturityDate', value: 'maturityDate' },
  { label: 'Tenor (yr.)', sortBy: 'tenor', value: 'tenor' },
  { label: 'Pricing approach', sortBy: 'pricingApproach', value: 'pricingApproach' },
  { label: 'Created', sortBy: 'createdAt', value: 'createdAt' },
  { label: 'Updated', sortBy: 'updatedAt', value: 'updatedAt' },
  { label: 'Deleted', sortBy: 'deletedAt', value: 'deletedAt' },
  { label: 'Seniority', sortArray: seniorities, sortBy: 'seniority', sortType: 'array', value: 'seniority' },
  {
    label: 'Status',
    sortBy: 'status',
    value: 'status',
    renderCustomCell: (item) => {
      return (
        <Text color={reportStatusColors[item.status]} variant="m-spaced">
          {item.status}
        </Text>
      );
    },
  },
  { label: 'Rate', sortBy: 'finalInterestRate', value: 'finalInterestRate' },
  { label: 'Lower bound', sortBy: 'lowerBound', value: 'lowerBound' },
  { label: 'Base', sortBy: 'midPoint', value: 'midPoint' },
  { label: 'Upper bound', sortBy: 'upperBound', value: 'upperBound' },
  { label: 'Lender Type', sortBy: 'lenderType', value: 'lenderType' },
  { label: 'Note', sortBy: 'note', value: 'note' },
  { label: 'Unique ID', sortBy: 'uniqueId', value: 'uniqueId' },
];

export function getLoansSheetData(data = [], visibleColumns, userInfo) {
  return getSheetData({ data, columns, visibleColumns, userInfo, getColumnData: getLoanData });
}

export function getLoansColumns(visibleColumns) {
  return getVisibleColumns({ columns, visibleColumns });
}
