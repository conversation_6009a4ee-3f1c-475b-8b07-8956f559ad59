import { deleteUser, demoteToUser, getUsers, promoteToAdmin } from '~/api';
import { ThreeDotActionMenu } from '~/components/Shared';
import { rolesEnum } from '~/enums';
import { showErrorToast } from '~/ui/components/Toast';

const fullName = 'fullName';
const username = 'username';
const email = 'email';
const role = 'role';
const client = 'client';

const fullNameColumn = { label: 'Full name', sortBy: 'fullName', value: fullName };
const usernameColumn = { label: 'Username', sortBy: 'username', value: username };
const emailColumn = { label: 'Email', sortBy: 'email', value: email, width: 300 };
const roleColumn = { label: 'Role', sortBy: 'role', value: role };
const clientColumn = { label: 'Client', sortBy: 'client', value: client };

const adminColumns = [fullNameColumn, usernameColumn, emailColumn, roleColumn];
const getAdminUserData = ({ id, username, fullName, email, role }) => ({
  id,
  username,
  fullName,
  email,
  role,
});
const getAdminUsersData = (data = []) => data.map(getAdminUserData);

const superAdminColumns = [fullNameColumn, usernameColumn, emailColumn, clientColumn, roleColumn];
const getSuperAdminUserData = ({ id, username, fullName, email, role, client }) => ({
  id,
  username,
  fullName,
  email,
  role,
  client: client.name,
});
const getSuperAdminUsersData = (data = []) => data.map(getSuperAdminUserData);

export const getColumnsAndData = (role) =>
  role === rolesEnum.ADMIN ? [adminColumns, getAdminUsersData] : [superAdminColumns, getSuperAdminUsersData];

export const renderTableActionColumn = (item, users, setUsers, id, role) => {
  const options = [];

  if (item.role === rolesEnum.USER) {
    options.push({
      label: 'Promote to Admin',
      onClick: async () => {
        promoteToAdmin(item.id)
          .then(() => getUsers().then(setUsers))
          .catch(() => showErrorToast());
      },
    });
  }

  if (role === rolesEnum.SUPERADMIN && item.role === rolesEnum.ADMIN) {
    options.push({
      label: 'Demote to User',
      onClick: () => {
        demoteToUser(item.id)
          .then(() => getUsers().then(setUsers))
          .catch(() => showErrorToast());
      },
    });
  }

  if (role === rolesEnum.SUPERADMIN && item.id !== id) {
    options.push({
      label: 'Delete',
      onClick: () => {
        deleteUser(item.id)
          .then(() => setUsers(users.filter((u) => u.id !== item.id)))
          .catch(() => showErrorToast());
      },
    });
  }

  if (!options.length) return null;

  return <ThreeDotActionMenu options={options} />;
};
