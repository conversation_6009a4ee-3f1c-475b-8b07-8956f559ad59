import { useContext, useEffect, useState } from 'react';

import { createUser, getUsers } from '~/api';
import { UserInfoContext } from '~/context/user';
import { isAdmin } from '~/enums';
import { Button, Card, FlexLayout, PageLayout, Table } from '~/ui';
import { showToast, showWarningToast } from '~/ui/components/Toast';
import { errorHandler } from '~/utils/errors';

import { CreateUserModal } from '../Modals';
import { getColumnsAndData, renderTableActionColumn } from './UsersPage.utils';
import { rolesEnum } from 'enums';

const azureString = 'azure';
const usernameString = 'username';

const UserPage = () => {
  const { userInfo } = useContext(UserInfoContext);
  const [users, setUsers] = useState();
  const [isCreateUserModalShowing, setIsCreateUserModalShowing] = useState(false);
  const [shouldConfirmUserCreate, setShouldConfirmUserCreate] = useState(false);
  const [columns, getUserData] = getColumnsAndData(userInfo.role);

  const getCreateUserData = ({ provider, username, password, email, fullName, role, clientId }) => {
    const clientIdOverride = userInfo.role === rolesEnum.SUPERADMIN ? clientId : null;

    if (provider === 'username') {
      return { provider, username, password, email, fullName, role, clientId: clientIdOverride };
    }
    if (provider === 'azure') {
      return { provider, email, fullName, role, clientId: clientIdOverride };
    }

    throw new Error('Unknown provider.');
  };

  const onCreateUser = async (data) => {
    try {
      const newUser = await createUser(getCreateUserData(data), shouldConfirmUserCreate);
      setUsers([...users, newUser]);
      showToast('User created successfully');
      setIsCreateUserModalShowing(false);
      setShouldConfirmUserCreate(false);
    } catch (err) {
      if (err.response.status === 409) {
        setShouldConfirmUserCreate(true);
        return showWarningToast(
          'User email address domain does not match the domain of the first user created. Proceed with caution.'
        );
      }
      errorHandler(err);
    }
  };

  useEffect(() => {
    getUsers().then(setUsers);
  }, []);

  return (
    <PageLayout
      title="Users"
      rightTitleContent={
        <>
          {isAdmin(userInfo.role) && (
            <FlexLayout alignItems="center" space={6}>
              <Button
                iconLeft="add"
                text="New user"
                size="s"
                variant="secondary"
                onClick={() => setIsCreateUserModalShowing(true)}
              />
            </FlexLayout>
          )}
        </>
      }
    >
      <Card pb={3} pt={6}>
        <FlexLayout flexDirection="column">
          <Table
            isSearchable
            actionColumn={(item) => renderTableActionColumn(item, users, setUsers, userInfo.id, userInfo.role)}
            columns={columns}
            data={getUserData(users)}
          />
        </FlexLayout>
      </Card>
      <CreateUserModal
        azureString={azureString}
        usernameString={usernameString}
        isShowing={isCreateUserModalShowing}
        onHide={() => setIsCreateUserModalShowing(false)}
        shouldConfirmUserCreate={shouldConfirmUserCreate}
        setShouldConfirmUserCreate={setShouldConfirmUserCreate}
        onCreateUser={onCreateUser}
      />
    </PageLayout>
  );
};

export default UserPage;
