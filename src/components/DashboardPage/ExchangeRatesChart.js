import axios from 'axios';
import { format, sub } from 'date-fns';
import { useContext, useEffect, useState } from 'react';
import { Line } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, PointElement, LineElement, Tooltip, Legend } from 'chart.js';

import { getHistoricRatesBetweenCurrencies } from '~/api';
import { UserInfoContext } from '~/context/user';
import { DATE_FNS_FORMATS } from '~/enums/dates';
import { Box, DateInput, FlexLayout } from '~/ui';
import colors from '~/ui/theme/colors';
import { displayNumber2 } from '~/utils/strings';

import { CurrencySingleSelect } from '../Shared';
ChartJS.register(CategoryScale, LinearScale, PointElement, LineElement, Tooltip, Legend);

function ExchangeRatesGraph() {
  const { userInfo } = useContext(UserInfoContext);
  const [data, setData] = useState(null);
  const [currency1, setCurrency1] = useState('EUR');
  const [currency2, setCurrency2] = useState('USD');
  const [startDate, setStartDate] = useState(format(sub(new Date(), { months: 1 }), DATE_FNS_FORMATS.ISO));
  const [endDate, setEndDate] = useState(format(new Date(), DATE_FNS_FORMATS.ISO));

  const getGraphOptions = () => ({
    scales: {
      yAxes: {
        ticks: {
          callback: (value) => displayNumber2(value, { decimalPoint: userInfo.decimalPoint }),
        },
      },
    },
    tooltips: {
      callbacks: {
        label: (tooltipItem, data) => {
          const { label = '' } = data.datasets[tooltipItem.datasetIndex];
          return `${label}: ${displayNumber2(tooltipItem.yLabel, { decimalPoint: userInfo.decimalPoint })}`;
        },
      },
    },
  });

  useEffect(() => {
    const source = axios.CancelToken.source();
    const cancelToken = source.token;
    const params = {
      start_date: format(new Date(startDate), DATE_FNS_FORMATS.ISO),
      end_date: format(new Date(endDate), DATE_FNS_FORMATS.ISO),
      base: currency1,
      symbols: currency2,
    };

    getHistoricRatesBetweenCurrencies(params, cancelToken)
      .then((res) => {
        const rates = res.data.rates;
        const lineGraphData = {
          labels: Object.keys(rates).map((rate) => format(new Date(rate), DATE_FNS_FORMATS[userInfo.dateFormat])),
          datasets: [
            {
              data: Object.keys(rates).map((key) => rates[key][currency2]),
              label: `${currency1} to ${currency2}`,
              borderColor: colors['shakespeare'],
              backgroundColor: colors['shakespeare'],
              tension: 0.4,
            },
          ],
        };
        setData(lineGraphData);
      })
      .catch(() => {});

    return () => source.cancel();
  }, [currency1, currency2, startDate, endDate, userInfo.dateFormat]);

  if (!data) return null;

  return (
    <FlexLayout flexDirection="column" space={4} dataTestId="exchangeRatesContainer">
      <Box sx={{ display: 'grid', gridGap: 4, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        <CurrencySingleSelect
          label="From Currency"
          value={currency1}
          variant="short"
          width="fullWidth"
          onChange={setCurrency1}
        />
        <CurrencySingleSelect
          label="To Currency"
          value={currency2}
          variant="short"
          width="fullWidth"
          onChange={setCurrency2}
        />
        <DateInput
          label="Start Date"
          maxDate={new Date()}
          value={startDate}
          width="fullWidth"
          onChange={setStartDate}
        />
        <DateInput label="End Date" maxDate={new Date()} value={endDate} width="fullWidth" onChange={setEndDate} />
      </Box>
      <Line data={data} options={getGraphOptions()} />
    </FlexLayout>
  );
}

export default ExchangeRatesGraph;
