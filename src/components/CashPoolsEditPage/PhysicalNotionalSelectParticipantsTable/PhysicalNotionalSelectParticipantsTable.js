import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { UpdateCirDirModal, UpdateCurrencyModal, ExternalIdsModal } from '~/components/Modals';
import { ReportsCard, SelectParticipantsTable } from '~/components/Shared';
import { NOTIONAL, PHYSICAL } from '~/enums';
import { useCompanies } from '~/hooks';
import { cashPoolSelector, updateField } from '~/reducers/cashPool.slice';
import { cashPoolTopCurrencyAccountSelector } from '~/reducers/cashPoolTopCurrencyAccount.slice';
import { Pagination } from '~/ui';

import AccountBalanceModal from './AccountBalanceModal';

const PhysicalNotionalSelectParticipantsTable = () => {
  const dispatch = useDispatch();
  const companies = useCompanies();
  const [inTableParticipants, setInTableParticipants] = useState(); // combination of company and participant data
  const [isCirDirModalShowing, setIsCirDirModalShowing] = useState(); // false or set to table row values of participant
  const [isCurrencyModalShowing, setIsCurrencyModalShowing] = useState(); // false or set to table row values of participant
  const [isBalanceModalShowing, setIsBalanceModalShowing] = useState(); // false or set to table row values of participant
  const [isExternalIdsModalShowing, setIsExternalIdsShowing] = useState(); // false or set to table row values of participant
  const { leaderId, participantsOffset, interestType, type } = useSelector(cashPoolSelector);
  const topCurrencyAccount = useSelector(cashPoolTopCurrencyAccountSelector);
  const accounts = topCurrencyAccount?.accounts;
  const isPhysical = type === PHYSICAL;
  const isNotional = type === NOTIONAL;
  const isFixed = interestType === 'fixed';
  const offset = participantsOffset || 0;
  const limit = 10;
  const isShowing = (isPhysical || isNotional) && leaderId;

  const onPageChange = ({ selected }) => dispatch(updateField({ participantsOffset: selected }));

  useEffect(() => {
    const inTableParticipants = [];
    for (const company of companies) {
      const companyCopy = { ...company };
      const account = accounts.find((account) => account.companyId === companyCopy.id);
      if (account) {
        companyCopy.accountId = account.id;
        companyCopy.creditInterestRate = account.creditInterestRate;
        companyCopy.debitInterestRate = account.debitInterestRate;
        companyCopy.generateInterestStatementData = account.generateInterestStatementData;
        companyCopy.currency = account.currency;
        companyCopy.value = true; // used in getCompaniesSheetData to know which companies are selected (company.value ? 'Yes' : 'No';)
      }
      inTableParticipants.push(companyCopy);
    }
    setInTableParticipants(inTableParticipants);
  }, [companies, leaderId, accounts]);

  if (!isShowing) return null;

  return (
    <ReportsCard
      title="Select Cash Pool Participants"
      table={
        <>
          <SelectParticipantsTable
            inTableParticipants={inTableParticipants}
            accounts={accounts}
            setIsCirDirModalShowing={setIsCirDirModalShowing}
            setIsCurrencyModalShowing={setIsCurrencyModalShowing}
            setIsBalanceModalShowing={setIsBalanceModalShowing}
            setIsExternalIdsShowing={setIsExternalIdsShowing}
            isFixed={isFixed}
            type={type}
            companies={companies}
            limit={limit}
            participantsOffset={offset}
          />
          <Pagination
            canNextPage={companies.length - offset * limit > offset * limit}
            canPreviousPage={offset !== 0}
            pageCount={companies.length / limit}
            forcePage={offset}
            onPageChange={onPageChange}
            isShowing={companies.length > limit}
          />
          <UpdateCirDirModal
            onHide={() => setIsCirDirModalShowing(false)}
            participant={isCirDirModalShowing}
            interestType={interestType}
          />
          <UpdateCurrencyModal
            onHide={() => setIsCurrencyModalShowing(false)}
            participant={isCurrencyModalShowing}
            interestType={interestType}
          />
          <AccountBalanceModal onHide={() => setIsBalanceModalShowing(false)} participant={isBalanceModalShowing} />
          <ExternalIdsModal onHide={() => setIsExternalIdsShowing(false)} participant={isExternalIdsModalShowing} />
        </>
      }
    />
  );
};

export default PhysicalNotionalSelectParticipantsTable;
