import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import _ from 'lodash';

import { postCreditRating, createCreditRatingFile } from '~/api';
import { REPORT_TYPE, reportEnum } from '~/enums';
import { useCompanies } from '~/hooks';
import {
  creditRating,
  isCreditRatingFormValid,
  resetCreditRating,
  setCreditRatingData,
  setIsPristine,
  transform,
  updateField,
} from '~/reducers/creditRating.slice';
import { showErrorToast, showToast } from '~/ui/components/Toast';
import { Button, FlexLayout } from '~/ui';
import { WithTooltip } from '~/ui/hocs';
import { errorHandler } from '~/utils/errors';

import CreditRatingForm from './CreditRatingForm';

function CreditRatingPage() {
  const dispatch = useDispatch();
  const data = useSelector(creditRating);
  const history = useHistory();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedTemplateFile, setUploadedTemplateFile] = useState(null);
  useCompanies(); // refresh company list

  useEffect(() => {
    dispatch(setCreditRatingData());

    return () => {
      dispatch(resetCreditRating());
    };
  }, [dispatch]);

  async function handleOnSubmitClick() {
    const errors = isCreditRatingFormValid(data);

    if (Object.keys(errors).length) {
      showErrorToast('One or more fields are invalid.');
      dispatch(updateField({ errors: errors, showErrors: true }));
      return;
    }
    setIsSubmitting(true);

    try {
      const requestData = transform(data);
      const createdCreditRating = await postCreditRating(requestData);
      showToast('Credit rating was successfully created.');
      dispatch(setIsPristine());
      if (uploadedTemplateFile) {
        await createCreditRatingFile({
          reportId: createdCreditRating.id,
          file: uploadedTemplateFile,
          label: 'Other',
          status: 'Final',
        });
      }
      history.push(`/analyses/${createdCreditRating.id}?${REPORT_TYPE}=${reportEnum.CREDIT_RATING}`);
    } catch (error) {
      errorHandler(error);
    } finally {
      setIsSubmitting(false);
    }
  }

  const isSubmitDisabled = (data?.showErrors && Object.keys(data?.errors).length !== 0) || _.isEmpty(data.company);

  return (
    <CreditRatingForm
      actionButtons={
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="flex-end">
          <WithTooltip
            tooltip="Company needs to be picked and all errors<br /> need to be resolved before creating an analysis."
            label="Create analysis"
            disabled={isSubmitDisabled}
          >
            <Button
              disabled={isSubmitDisabled}
              loading={isSubmitting}
              iconRight="arrowRight"
              text="Create analysis"
              onClick={isSubmitting ? null : handleOnSubmitClick}
            />
          </WithTooltip>
        </FlexLayout>
      }
      setUploadedTemplateFile={setUploadedTemplateFile}
    />
  );
}

export default CreditRatingPage;
