import { useState } from 'react';
import { useAppSelector } from 'hooks';

import { Button, DateInput, FlexLayout, Modal, NumberInput, SingleSelect } from 'ui';
import { cashPoolSelector } from 'reducers/cashPool.slice';
import { format } from 'date-fns';

type AddStatementDataModalPropsType = {
  dataTestId?: string;
  isShowing: boolean;
  handleOnAdd: ({
    cashPoolAccountId,
    date,
    statementDate,
    balanceChange,
    cashPoolId,
  }: {
    cashPoolAccountId: number;
    date: string;
    statementDate: string;
    balanceChange: number;
    cashPoolId: number;
  }) => Promise<void>;
  handleOnHide: () => void;
};

function AddStatementDataModal({
  isShowing,
  dataTestId = '',
  handleOnAdd,
  handleOnHide,
}: AddStatementDataModalPropsType) {
  const cashPool = useAppSelector(cashPoolSelector);
  const [cashPoolAccountId, setCashPoolAccountId] = useState<number | null>(null);
  const [valueDate, setValueDate] = useState<string | null>(null);
  const [bookDate, setBookDate] = useState<string | null>(null);
  const [balanceChange, setBalanceChange] = useState<number | null>(null);

  const getOptions = () => {
    if (!cashPool) return [];

    return cashPool.topCurrencyAccounts[0].accounts.map(({ id, participant }: any) => ({
      value: id,
      label: participant.company.name,
    }));
  };

  const resetFields = () => {
    setCashPoolAccountId(null);
    setValueDate(null);
    setBookDate(null);
    setBalanceChange(null);
  };

  const onAddClick = async () => {
    try {
      await handleOnAdd({
        cashPoolAccountId: Number(cashPoolAccountId),
        date: format(new Date(String(valueDate)), 'yyyy-MM-dd'),
        statementDate: format(new Date(String(bookDate)), 'yyyy-MM-dd'),
        balanceChange: Number(balanceChange),
        cashPoolId: Number(cashPool.id),
      });
      resetFields();
    } catch (err) {}
  };

  const onHideClick = () => {
    resetFields();
    handleOnHide();
  };

  if (!isShowing) return null;

  return (
    <Modal
      actionButtons={
        <Button
          text="Add"
          onClick={onAddClick}
          disabled={cashPoolAccountId == null || valueDate == null || bookDate == null || balanceChange == null}
          dataTestId={dataTestId}
        />
      }
      title="Add statement data"
      width="s"
      onHide={onHideClick}
    >
      <FlexLayout flexDirection="column" justifyContent="space-between" sx={{ gap: '12px' }}>
        <FlexLayout space={4}>
          <SingleSelect
            label="Company"
            value={cashPoolAccountId}
            onChange={setCashPoolAccountId}
            options={getOptions()}
          />
          <NumberInput label="Balance Change" inputType="float" value={balanceChange} onChange={setBalanceChange} />
        </FlexLayout>
        <FlexLayout space={4}>
          <DateInput label="Value Date" value={valueDate} onChange={setValueDate} />
          <DateInput label="Statement Date (Book Date)" value={bookDate} onChange={setBookDate} />
        </FlexLayout>
      </FlexLayout>
    </Modal>
  );
}

export default AddStatementDataModal;
