import { useContext, useEffect, useState } from 'react';

import { Button, DateInput, FlexLayout, Modal, Text, TextInput } from 'ui';
import { TableRowType } from './StatementDataTable.utils';
import { UserInfoContext } from 'context';
import { formatDateString } from 'utils/dates';
import { showErrorToast } from 'ui/components/Toast';

type EditStatementDataModalPropsType = {
  dataTestId?: string;
  isShowing: false | TableRowType;
  handleOnEdit: ({
    statementId,
    date,
    statementDate,
    comment,
  }: {
    statementId: number;
    date: string;
    statementDate: string;
    comment?: string;
  }) => Promise<void>;
  handleOnHide: () => void;
};

function EditStatementDataModal({
  isShowing,
  dataTestId = '',
  handleOnEdit,
  handleOnHide,
}: EditStatementDataModalPropsType) {
  const { userInfo } = useContext(UserInfoContext);
  const [statementDate, setStatementDate] = useState<string | null>(null);
  const [date, setDate] = useState<string | null>(null);
  const [comment, setComment] = useState<string>('');

  const resetFields = () => {
    setStatementDate(null);
    setDate(null);
    setComment('');
  };

  const onSubmit = async () => {
    if (!isShowing) return;
    if (!date || !statementDate) return showErrorToast('Please fill in date and statement date.');

    await handleOnEdit({ statementId: isShowing.id, date, statementDate, comment });
    resetFields();
  };

  const onHideClick = () => {
    resetFields();
    handleOnHide();
  };

  useEffect(() => {
    if (isShowing) {
      setDate(isShowing.date ?? null);
      setStatementDate(isShowing.statementDate ?? null);
      setComment(isShowing.comment ?? '');
    }
  }, [isShowing]);

  if (!isShowing) return null;

  return (
    <Modal
      actionButtons={
        <Button
          text="Submit"
          onClick={onSubmit}
          disabled={date == null || statementDate == null}
          dataTestId={dataTestId}
        />
      }
      title="Edit statement data"
      width="s"
      onHide={onHideClick}
    >
      <FlexLayout flexDirection="column" justifyContent="space-between" sx={{ gap: '12px' }}>
        <Text color="deep-sapphire">Value date: {formatDateString(isShowing.date, userInfo.dateFormat)}</Text>
        <Text color="deep-sapphire">
          Statement date: {formatDateString(isShowing.statementDate, userInfo.dateFormat)}
        </Text>
        <Text color="deep-sapphire">Balance change: {isShowing.balanceChange}</Text>
        <Text color="deep-sapphire">{isShowing.companyName}</Text>
        <FlexLayout space={4}>
          <DateInput label="Value Date" value={date} onChange={setDate} />
          <DateInput label="Statement Date" value={statementDate} onChange={setStatementDate} />
        </FlexLayout>
        <TextInput label="Comment" value={comment} onChange={setComment} width="fullWidth" />
      </FlexLayout>
    </Modal>
  );
}

export default EditStatementDataModal;
