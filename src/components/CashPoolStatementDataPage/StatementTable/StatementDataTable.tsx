import { useCallback, useContext, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { saveAs } from 'file-saver';

import {
  getCashPoolStatementData,
  createCashPoolStatementData,
  editCashPoolStatementData,
  exportCashPoolStatementData,
  deleteCashPoolStatementData,
  massDeleteCashPoolStatementData,
} from 'api';
import {
  statementDataFiltersSelector,
  updateField,
  updatePagination,
  resetStatementDataFilter,
} from 'reducers/statementDataFilters.slice';
import { DeleteModal } from 'components/Modals';
import { UserInfoContext } from 'context/user';
import { Button, FlexLayout, LoadingSpinner, Pagination, SingleSelect, Table } from 'ui';
import { errorHandler } from 'utils/errors';
import { showToast } from 'ui/components/Toast';
import { StatementDataType } from 'types';

import { columns, getCashPoolStatementsData, renderTableActionColumn, TableRowType } from './StatementDataTable.utils';
import StatementDataFilterForm from './StatementDataFilterForm';
import AddStatementDataModal from './AddStatementDataModal';
import EditStatementDataModal from './EditStatementDataModal';
import { useAppDispatch, useAppSelector } from 'hooks';

const StatementsTable = () => {
  const dispatch = useAppDispatch();
  const statementDataFilters = useAppSelector(statementDataFiltersSelector);
  const { cashPoolId } = useParams<{ cashPoolId: string }>();
  const { userInfo } = useContext(UserInfoContext);
  const [statementData, setStatementData] = useState<Array<StatementDataType>>();
  const [statementDataCount, setStatementDataCount] = useState(0);
  const [isDeleteModalShowing, setIsDeleteModalShowing] = useState<TableRowType | false>(false);
  const [isMassDeleteModalShowing, setIsMassDeleteModalShowing] = useState<boolean>(false);
  const [isEditStatementModalShowing, setIsEditStatementModalShowing] = useState<TableRowType | false>(false);
  const [isAddStatementModalShowing, setIsAddStatementModalShowing] = useState(false);
  const [isFilterFormShowing, setIsFilterFormShowing] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [selectedStatementIds, setSelectedStatementIds] = useState<number[]>([]);
  const { sort, limit, offset } = statementDataFilters;

  const getAndSetStatements = useCallback(async () => {
    getCashPoolStatementData({ cashPoolId, ...statementDataFilters })
      .then(({ data, count }: any) => {
        setStatementData(data);
        setStatementDataCount(count);
      })
      .catch(errorHandler);
  }, [cashPoolId, statementDataFilters]);

  const handleOnDeleteClick = async () => {
    try {
      if (!isDeleteModalShowing) return;
      await deleteCashPoolStatementData({ cashPoolId, statementDataId: isDeleteModalShowing.id });
      setSelectedStatementIds((prev) => prev.filter((id) => id !== isDeleteModalShowing.id));
      await getAndSetStatements();
      setIsDeleteModalShowing(false);
      showToast('Successfully deleted.');
    } catch (error) {
      errorHandler(error);
    }
  };

  const handleOnMassDeleteClick = async () => {
    try {
      if (selectedStatementIds.length === 0) return;
      await massDeleteCashPoolStatementData({ cashPoolId, statementDataIds: selectedStatementIds });
      await getAndSetStatements();
      setSelectedStatementIds([]);
      setIsMassDeleteModalShowing(false);
      showToast('Successfully deleted.');
    } catch (error) {
      errorHandler(error);
    }
  };

  const handleOnAdd = async (data: {
    cashPoolAccountId: number;
    date: string;
    statementDate: string;
    balanceChange: number;
  }) => {
    try {
      await createCashPoolStatementData({ cashPoolId, data });
      await getAndSetStatements();
      setIsAddStatementModalShowing(false);
      showToast('Successfully created.');
    } catch (error) {
      errorHandler(error);
      throw error;
    }
  };

  const handleOnEdit = async (data: { statementId: number; date: string; statementDate: string; comment?: string }) => {
    await editCashPoolStatementData({ cashPoolId, data });
    await getAndSetStatements();
    setIsEditStatementModalShowing(false);
    showToast('Successfully updated.');
  };

  const onExportClick = async () => {
    try {
      setIsExporting(true);
      const file = await exportCashPoolStatementData({ cashPoolId, ...statementDataFilters });
      saveAs(file, file.name);
      showToast('Successfully exported.');
    } catch (error) {
      errorHandler(error);
    } finally {
      setIsExporting(false);
    }
  };

  const toggleAllCheckboxes = (isActive: boolean) => {
    if (isActive && statementData) {
      setSelectedStatementIds(statementData.map((item) => item.id));
    } else {
      setSelectedStatementIds([]);
    }
  };

  const toggleCheckbox = (id: number, isActive: boolean) => {
    if (isActive) {
      setSelectedStatementIds((prev) => [...prev, id]);
    } else {
      setSelectedStatementIds((prev) => prev.filter((itemId) => itemId !== id));
    }
  };

  useEffect(() => {
    getAndSetStatements();
  }, [getAndSetStatements]);

  if (!statementData) return <LoadingSpinner />;

  const allSelected = statementData
    ? statementData.length > 0 && selectedStatementIds.length === statementData.length
    : false;

  return (
    <>
      <FlexLayout space={4}>
        <Button
          text="Add"
          iconLeft="add"
          size="s"
          variant="secondary"
          onClick={() => setIsAddStatementModalShowing(true)}
        />
        <Button
          text="Export"
          iconLeft="export"
          size="s"
          variant="secondary"
          loading={isExporting}
          onClick={onExportClick}
        />
        <Button
          iconLeft="filter"
          size="s"
          text="Filter"
          variant="secondary"
          onClick={() => setIsFilterFormShowing(!isFilterFormShowing)}
        />
        <Button
          iconLeft="delete"
          size="s"
          text="Clear"
          variant="secondary"
          onClick={() => dispatch(resetStatementDataFilter())}
        />
        <Button
          iconLeft="delete"
          size="s"
          disabled={selectedStatementIds.length === 0}
          text={`Delete Selected (${selectedStatementIds.length})`}
          variant="secondary"
          onClick={() => setIsMassDeleteModalShowing(true)}
        />
      </FlexLayout>

      <StatementDataFilterForm isShowing={isFilterFormShowing} />

      <Table
        actionColumn={(item: TableRowType) =>
          renderTableActionColumn({ item, setIsDeleteModalShowing, setIsEditStatementModalShowing })
        }
        columns={columns({
          selectedStatementIds,
          toggleCheckbox,
          allSelected,
          toggleAllCheckboxes,
        })}
        data={getCashPoolStatementsData(statementData, userInfo)}
        isPaginationHidden
        serverSideSorting
        updateSorting={(id: string) => dispatch(updateField({ sort: id }))}
        sortedBy={sort}
        customPageSize={limit}
      />

      <SingleSelect
        label="Items per page"
        width="s"
        options={[
          {
            value: 5,
            label: '5',
          },
          {
            value: 10,
            label: '10',
          },
          {
            value: 25,
            label: '25',
          },
          {
            value: 50,
            label: '50',
          },
          {
            value: 100,
            label: '100',
          },
        ]}
        value={limit}
        onChange={(itemsPerPage: string) => {
          setSelectedStatementIds([]);
          dispatch(updateField({ limit: itemsPerPage }));
        }}
      />
      <Pagination
        canNextPage={statementDataCount - offset * limit > offset * limit}
        canPreviousPage={offset !== 0}
        pageCount={statementDataCount / limit}
        forcePage={offset}
        onPageChange={({ selected }: { selected: number }) => {
          setSelectedStatementIds([]);
          dispatch(updatePagination({ offset: selected }));
        }}
        isShowing={statementDataCount > limit}
      />
      <DeleteModal
        isShowing={!!isDeleteModalShowing}
        item="statement data"
        handleOnDeleteClick={handleOnDeleteClick}
        handleOnHide={() => setIsDeleteModalShowing(false)}
      />
      <DeleteModal
        isShowing={!!isMassDeleteModalShowing}
        item="statement data"
        handleOnDeleteClick={handleOnMassDeleteClick}
        handleOnHide={() => setIsMassDeleteModalShowing(false)}
      />
      <AddStatementDataModal
        isShowing={isAddStatementModalShowing}
        handleOnAdd={handleOnAdd}
        handleOnHide={() => setIsAddStatementModalShowing(false)}
      />
      <EditStatementDataModal
        isShowing={isEditStatementModalShowing}
        handleOnEdit={handleOnEdit}
        handleOnHide={() => setIsEditStatementModalShowing(false)}
      />
    </>
  );
};

export default StatementsTable;
