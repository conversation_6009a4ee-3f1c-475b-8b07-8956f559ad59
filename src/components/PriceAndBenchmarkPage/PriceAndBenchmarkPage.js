import { saveAs } from 'file-saver';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { routesEnum } from '~/routes';
import { createB2BLoan, createGuarantee, createLoan } from '~/api';
import { PricingApproachModal } from '~/components/Modals';
import { UserInfoContext } from '~/context/user';
import { REPORT_TYPE, reportEnum, rolesEnum } from '~/enums';
import { useCompanies, useUnsavedChangesWarning } from '~/hooks';
import {
  isReportFormValid,
  report,
  resetReport,
  setIsPristine,
  setIsSubmitting,
  setReportData,
  transformGuarantee,
  transformLoan,
  transformBackToBackLoan,
  updateField,
} from '~/reducers/report.slice';
import { But<PERSON>, Card, FlexLayout, PageLayout, Tabs, FileInput } from '~/ui';
import { showErrorToast, showToast } from '~/ui/components/Toast';
import { areRublesDiscontinued } from '~/utils/report';
import { capitalize } from '~/utils/strings';
import { genericTabSetter } from '~/utils/tabs';
import { errorHandler } from '~/utils/errors';
import { sheetToJson } from '~/utils/documents';

import GuaranteeForm from './GuaranteeForm';
import LoanForm from './LoanForm';
import BackToBackLoanForm from './BackToBackLoanForm';
import {
  reportsDownloadTemplateMethodMapper,
  reportsUploadTemplateMethodMapper,
  getPricingOptions,
} from './PriceAndBenchmarkPage.utils';
import onFillDataClick from './fillData';

function PriceAndBenchmarkPage() {
  const dispatch = useDispatch();
  const history = useHistory();
  const data = useSelector(report);
  const [pricingOptions, setPricingOptions] = useState([]);
  const [tabs, setTabs] = useState();
  const [isUploading, setIsUploading] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [Prompt] = useUnsavedChangesWarning({ isDirty: data.isDirty });
  const { userInfo } = useContext(UserInfoContext);
  const companies = useCompanies();
  const isSuperadmin = userInfo.role === rolesEnum.SUPERADMIN;

  const handleOnTabSelect = useCallback((reportType) => dispatch(setReportData({ reportType })), [dispatch]);

  useEffect(() => {
    dispatch(setReportData({ reportType: reportEnum.LOAN }));

    return () => {
      dispatch(resetReport());
    };
  }, [dispatch, history]);

  async function onTemplateDownloadClick() {
    try {
      const getReportUploadTemplate = reportsDownloadTemplateMethodMapper[data?.reportType];

      const res = await getReportUploadTemplate();
      saveAs(res, `${data.reportType.replace(/\s+/g, '_')}_upload_template.xlsx`);
      showToast('Template was successfully downloaded.');
    } catch (error) {
      errorHandler(error);
    }
  }

  async function onTemplateUploadClick(document) {
    try {
      setIsUploading(true);
      const jsonSheet = await sheetToJson(new Uint8Array(await document.arrayBuffer()), null);

      const reports = reportsUploadTemplateMethodMapper[data.reportType].getReportsFromSheet(jsonSheet, companies);

      /** Fill form on this page if only one report was in sheet. Otherwise create reports and go to analyses page */
      if (reports.length === 1) {
        return dispatch(setReportData({ reportType: data.reportType, ...reports[0] }));
      }

      await reportsUploadTemplateMethodMapper[data.reportType].createReports(reports);
      showToast(`${capitalize(data.reportType)}s were successfully uploaded.`);
      history.push(`${routesEnum.ANALYSES}?${REPORT_TYPE}=${data.reportType}`);
    } catch (error) {
      errorHandler(error);
    } finally {
      setIsUploading(false);
    }
  }

  function handleOnSubmitClick() {
    if (areRublesDiscontinued({ date: data.issueDate, currency: data.currency })) {
      return showErrorToast('RUB data is discontinued from 15 March 2022');
    }

    dispatch(setIsSubmitting(true));
    const createReport = data?.reportType === reportEnum.LOAN ? createLoan : createGuarantee;

    createReport(data?.reportType === reportEnum.LOAN ? transformLoan(data) : transformGuarantee(data))
      .then((res) => {
        showToast(`${capitalize(data?.reportType)} has been successfully created.`);
        dispatch(setIsPristine());
        history.push(`/analyses/${res.id}?${REPORT_TYPE}=${data?.reportType}`);
      })
      .catch(errorHandler)
      .finally(() => dispatch(setIsSubmitting(false)));
  }

  const handleBackToBackSubmit = async () => {
    try {
      dispatch(setIsSubmitting(true));
      const res = await createB2BLoan(transformBackToBackLoan(data));
      showToast(`${capitalize(data.reportType)} has been successfully created.`);
      dispatch(setIsPristine());
      history.push(`/analyses/${res.id}?${REPORT_TYPE}=${data.reportType}`);
    } catch (err) {
      dispatch(setIsSubmitting(false));
      errorHandler(err);
    }
  };

  function onNextClick() {
    if (data.reportType === reportEnum.BACK_TO_BACK_LOAN) {
      return handleBackToBackSubmit();
    }
    const { pricingOptions, initialOption } = getPricingOptions(data);
    dispatch(updateField({ pricingApproach: data.pricingApproach ?? initialOption }));
    setPricingOptions(pricingOptions);
    setShowModal(true);
  }

  useEffect(() => {
    const { features } = userInfo;
    const tabs = [
      { isEnabled: features.loan, label: 'loan', value: reportEnum.LOAN, dataTestId: 'loanTab' },
      {
        isEnabled: features.backToBackLoan,
        label: 'back-to-back loan',
        value: reportEnum.BACK_TO_BACK_LOAN,
        dataTestId: 'backToBackLoanTab',
      },
      {
        isEnabled: features.guarantee,
        label: 'guarantee',
        value: reportEnum.GUARANTEE,
        dataTestId: 'guaranteeTab',
      },
    ];
    genericTabSetter(setTabs, handleOnTabSelect, tabs);
  }, [userInfo, handleOnTabSelect]);

  return (
    <>
      <PageLayout
        title="Price and Benchmark"
        rightTitleContent={
          <FlexLayout alignItems="center" space={6}>
            <Button
              iconLeft="download"
              size="s"
              text="Download template"
              variant="secondary"
              onClick={onTemplateDownloadClick}
            />
            <FileInput accept=".xls,.xlsx" sx={{ alignSelf: 'center' }} onChange={onTemplateUploadClick}>
              <Button iconLeft="upload" size="s" loading={isUploading} text="Upload template" variant="secondary" />
            </FileInput>
          </FlexLayout>
        }
      >
        <Card py={6}>
          <Tabs selectedTab={data.reportType} tabs={tabs} onTabSelect={handleOnTabSelect} />
          {data.reportType === reportEnum.LOAN && <LoanForm />}
          {data.reportType === reportEnum.BACK_TO_BACK_LOAN && <BackToBackLoanForm />}
          {data.reportType === reportEnum.GUARANTEE && <GuaranteeForm />}
        </Card>
        <FlexLayout alignItems="flex-end" flexGrow="1" justifyContent="flex-end">
          <Button
            disabled={!isReportFormValid(data)}
            iconRight="arrowRight"
            text="Next"
            onClick={onNextClick}
            dataTestId="priceLoanGuaranteeButton"
          />
        </FlexLayout>
      </PageLayout>
      {Prompt}
      <Button
        text="Fill data"
        sx={{ position: 'absolute', bottom: '100px', right: '20px' }}
        isShowing={isSuperadmin && [reportEnum.LOAN, reportEnum.GUARANTEE].includes(data.reportType)}
        onClick={() => onFillDataClick({ data, companies, dispatch })}
      />
      <PricingApproachModal
        pricingOptions={pricingOptions}
        onHide={() => setShowModal(false)}
        onSubmitClick={handleOnSubmitClick}
        loading={data.isSubmitting}
        isShowing={showModal}
        data={data}
        updateField={updateField}
      />
    </>
  );
}

export default PriceAndBenchmarkPage;
