import { useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import {
  CompanySingleSelect,
  CurrencySingleSelect,
  LoanTypeSingleSelect,
  PaymentFrequencySingleSelect,
  SenioritySingleSelect,
} from 'components/Shared';
import { UserInfoContext } from 'context/user';
import { report, updateCurrency, updateField, updateIssueDate, updateRateType } from 'reducers/report.slice';
import { Box, DateInput, FlexLayout, NumberInput, RadioGroup, SingleSelect } from 'ui';
import { checkIsDisabled } from 'utils/features';
import { LoanTypeType, PaymentFrequencyType, SeniorityType } from 'types';

import EndDateInput from './EndDateInput';
import { loanTooltips } from './PriceAndBenchmarkPage.utils';

function LoanForm() {
  const dispatch = useDispatch();
  const history = useHistory();
  const {
    lender,
    borrower,
    issueDate,
    maturityDate,
    currency,
    amount,
    rateType,
    type,
    paymentFrequency,
    seniority,
    dayCount,
  } = useSelector(report);
  const { userInfo } = useContext(UserInfoContext);
  const { features } = userInfo;
  const isEdit = history.location.pathname.includes('/edit');

  return (
    <FlexLayout flexDirection="column" space={8}>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <CompanySingleSelect
          isLoanApproachCalculated={userInfo.client.isLoanApproachCalculated}
          checkCreditRating
          label="Lender"
          dataTestId="lenderCompanySelect"
          excludedValues={[borrower?.id]}
          tooltip={loanTooltips.lender}
          disabled={checkIsDisabled(isEdit, features.loanNumber)}
          value={lender?.id}
          width="fullWidth"
          onChange={(value: number) => dispatch(updateField({ lender: value }))}
        />
        <CompanySingleSelect
          checkCreditRating
          label="Borrower"
          dataTestId="borrowerCompanySelect"
          excludedValues={[lender?.id]}
          tooltip={loanTooltips.borrower}
          disabled={checkIsDisabled(isEdit, features.loanNumber)}
          value={borrower?.id}
          width="fullWidth"
          onChange={(value: number) => dispatch(updateField({ borrower: value }))}
        />
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(16, 1fr)' }}>
        <DateInput
          label="Issue Date"
          dataTestId="issueDateInput"
          minDate={new Date('2014-07-01')}
          maxDate={new Date()}
          tooltip={loanTooltips.issueDate}
          value={issueDate}
          width="fullWidth"
          sx={{ gridColumn: 'span 3' }}
          onChange={(value: Date) => dispatch(updateIssueDate({ issueDate: value }))}
        />
        <EndDateInput />
        <Box sx={{ gridColumn: 'span 4' }}>
          <CurrencySingleSelect
            tooltip={loanTooltips.currency}
            dataTestId="currencySelect"
            width="fullWidth"
            limitCurrencies
            value={currency}
            onChange={(value: string) => dispatch(updateCurrency(value))}
          />
        </Box>
        <Box sx={{ gridColumn: 'span 4' }}>
          <NumberInput
            allowNegatives={false}
            dataTestId="principalAmountInput"
            label="Principal Amount"
            tooltip={loanTooltips.amount}
            value={amount}
            width="fullWidth"
            onChange={(value: number) => dispatch(updateField({ amount: value }))}
          />
        </Box>
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        <RadioGroup
          label="Interest Type"
          options={[
            { label: 'Fixed', value: 'fixed' },
            { label: 'Float', value: 'float' },
          ]}
          tooltip={loanTooltips.rateType}
          value={rateType?.type}
          width="fullWidth"
          onChange={(value) => dispatch(updateRateType({ rateType: { type: value } }))}
        />
        <PaymentFrequencySingleSelect
          currency={currency}
          issueDate={issueDate}
          endDate={maturityDate}
          rateType={rateType?.type}
          tooltip={loanTooltips.paymentFrequency}
          dataTestId="paymentFrequencySelect"
          value={paymentFrequency}
          width="fullWidth"
          onChange={(paymentFrequency: PaymentFrequencyType) => dispatch(updateField({ paymentFrequency }))}
        />
        <SenioritySingleSelect
          tooltip={loanTooltips.seniority}
          dataTestId="senioritySelect"
          value={seniority}
          width="fullWidth"
          onChange={(seniority: SeniorityType) => dispatch(updateField({ seniority }))}
        />
        <LoanTypeSingleSelect
          tooltip={loanTooltips.type}
          dataTestId="loanTypeSelect"
          value={type}
          width="fullWidth"
          onChange={(type: LoanTypeType) => dispatch(updateField({ type }))}
        />
        <SingleSelect
          label="Day Count"
          width="fullWidth"
          options={[
            { value: 'ACT/365', label: 'ACT/365' },
            { value: 'ACT/364', label: 'ACT/364' },
            { value: 'ACT/360', label: 'ACT/360' },
            { value: 'ACT/252', label: 'ACT/252' },
          ]}
          value={dayCount}
          onChange={(dayCount: string) => dispatch(updateField({ dayCount }))}
        />
      </Box>
    </FlexLayout>
  );
}

export default LoanForm;
