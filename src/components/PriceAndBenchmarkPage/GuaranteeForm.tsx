import { useContext } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import {
  CompanySingleSelect,
  CurrencySingleSelect,
  PaymentFrequencySingleSelect,
  SenioritySingleSelect,
} from 'components/Shared';
import { UserInfoContext } from 'context/user';
import { report, updateField, updateIssueDate } from 'reducers/report.slice';
import { Box, DateInput, FlexLayout, NumberInput, RadioGroup } from 'ui';
import { checkIsDisabled } from 'utils/features';
import { pricingMethodologyEnum } from 'enums';
import { PaymentFrequencyType, SeniorityType } from 'types';

import EndDateInput from './EndDateInput';
import { guaranteeTooltips } from './PriceAndBenchmarkPage.utils';

function GuaranteeForm() {
  const dispatch = useDispatch();
  const history = useHistory();
  const {
    guarantor,
    principal,
    issueDate,
    terminationDate,
    currency,
    paymentFrequency,
    seniority,
    amount,
    pricingMethodology,
  } = useSelector(report);
  const { userInfo } = useContext(UserInfoContext);
  const { features } = userInfo;
  const isEdit = history.location.pathname.includes('/edit');
  const { YIELD_EXPECTED_LOSS_APPROACH, SECURITY_APPROACH } = pricingMethodologyEnum;

  return (
    <FlexLayout flexDirection="column" space={8}>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <CompanySingleSelect
          checkCreditRating
          label="Guarantor"
          dataTestId="guarantorCompanySelect"
          excludedValues={[principal?.id]}
          tooltip={guaranteeTooltips.guarantor}
          disabled={checkIsDisabled(isEdit, features.guaranteeNumber)}
          value={guarantor?.id}
          width="fullWidth"
          onChange={(guarantor: number) => dispatch(updateField({ guarantor }))}
        />
        <RadioGroup
          label="Pricing Methodology"
          options={[
            { label: YIELD_EXPECTED_LOSS_APPROACH, value: YIELD_EXPECTED_LOSS_APPROACH },
            { label: SECURITY_APPROACH, value: SECURITY_APPROACH },
          ]}
          tooltip={guaranteeTooltips.pricingMethodology}
          width="fullWidth"
          value={pricingMethodology}
          onChange={(pricingMethodology: pricingMethodologyEnum) => {
            dispatch(updateField({ pricingMethodology, seniority: null }));

            /**
             * Remove principal if he doesn't have a probability of default (PoD) and Yield Expected is picked
             * Use case is: User picks Security Approach which shows all companies under principal and can pick
             * a company that doesn't have PoD. If the user then switches back to the Yield Expected the company
             * no longer exists in that dropdown, but is still in principal state of report.slice, so this is to
             * remove it from there too.
             */
            if (
              pricingMethodology === YIELD_EXPECTED_LOSS_APPROACH &&
              principal?.creditRating?.probabilityOfDefault == null
            ) {
              dispatch(updateField({ principal: {} }));
            }
          }}
        />
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(16, 1fr)' }}>
        <Box sx={{ gridColumn: 'span 8' }}>
          <CompanySingleSelect
            checkCreditRating={pricingMethodology === SECURITY_APPROACH}
            checkCreditRatingAndProbabilityOfDefault={pricingMethodology === YIELD_EXPECTED_LOSS_APPROACH}
            label="Principal"
            dataTestId="principalCompanySelect"
            excludedValues={[guarantor?.id]}
            tooltip={guaranteeTooltips.principal}
            disabled={checkIsDisabled(isEdit, features.guaranteeNumber)}
            value={principal?.id}
            width="fullWidth"
            onChange={(principal: number) => dispatch(updateField({ principal }))}
          />
        </Box>
        <DateInput
          label="Issue Date"
          dataTestId="issueDateInput"
          minDate={new Date('2014-07-01')}
          maxDate={new Date()}
          tooltip={guaranteeTooltips.issueDate}
          value={issueDate}
          width="fullWidth"
          sx={{ gridColumn: 'span 3' }}
          onChange={(issueDate: Date) => dispatch(updateIssueDate({ issueDate }))}
        />
        <EndDateInput />
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        <Box sx={{ gridColumn: 'span 1' }}>
          <CurrencySingleSelect
            tooltip={guaranteeTooltips.currency}
            dataTestId="currencySelect"
            width="fullWidth"
            limitCurrencies
            value={currency}
            onChange={(currency: string) => dispatch(updateField({ currency }))}
          />
        </Box>
        <Box sx={{ gridColumn: 'span 1' }}>
          <NumberInput
            allowNegatives={false}
            dataTestId="principalAmountInput"
            label="Amount"
            tooltip={guaranteeTooltips.amount}
            value={amount}
            width="fullWidth"
            onChange={(amount: number) => dispatch(updateField({ amount }))}
          />
        </Box>
        <Box sx={{ gridColumn: 'span 1' }}>
          <PaymentFrequencySingleSelect
            label="Payment Frequency"
            dataTestId="paymentFrequencySelect"
            issueDate={issueDate}
            endDate={terminationDate}
            rateType="fixed"
            tooltip={guaranteeTooltips.paymentFrequency}
            width="fullWidth"
            currency={null}
            value={paymentFrequency}
            onChange={(paymentFrequency: PaymentFrequencyType) => dispatch(updateField({ paymentFrequency }))}
          />
        </Box>
        <Box sx={{ gridColumn: 'span 1' }}>
          <SenioritySingleSelect
            tooltip={guaranteeTooltips.seniority}
            dataTestId="senioritySelect"
            width="fullWidth"
            isShowing={pricingMethodology === YIELD_EXPECTED_LOSS_APPROACH}
            value={seniority}
            onChange={(seniority: SeniorityType) => dispatch(updateField({ seniority }))}
          />
        </Box>
      </Box>
    </FlexLayout>
  );
}

export default GuaranteeForm;
