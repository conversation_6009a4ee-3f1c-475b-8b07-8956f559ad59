import { useEffect, useState } from 'react';
import { addDays } from 'date-fns';

import { getCashPoolPayments, getPaymentInterestDates, markCashPoolPaymentAsPaid } from 'api';
import { Box, Button, DateInput, Modal } from 'ui';
import { showToast } from 'ui/components/Toast';

type AddInterestToBalanceModalProps = {
  item: any | null;
  onHide: () => void;
  searchQuery: any;
  setPayments: any;
};

const AddInterestToBalanceModal = ({ item, onHide, searchQuery, setPayments }: AddInterestToBalanceModalProps) => {
  const [valueDate, setValueDate] = useState<string | null>(null);
  const [statementDate, setStatementDate] = useState<string | null>(null);

  const onSubmit = async () => {
    const { cashPoolId, batchId, paymentId } = item;
    const data = { isPaid: true, valueDate, statementDate };
    await markCashPoolPaymentAsPaid({ cashPoolId, batchId, paymentId, data });

    await getCashPoolPayments({ searchQuery })
      .then((cashPoolPaymentsWithCount: any) => setPayments({ cashPoolPaymentsWithCount }))
      .then(() => {
        showToast('Interest published');
        setValueDate(null);
        setStatementDate(null);
        onHide();
      });
  };

  useEffect(() => {
    if (!item) return;

    getPaymentInterestDates({ cashPoolId: item.cashPoolId }).then((endDate: string) => {
      setValueDate(endDate);
      setStatementDate(addDays(new Date(endDate), 1).toISOString().split('T')[0]);
    });
  }, [item]);

  if (!item) return null;

  return (
    <Modal
      actionButtons={<Button disabled={!valueDate || !statementDate} text="Submit" onClick={onSubmit} />}
      title="Edit value and statement date"
      width="s"
      onHide={onHide}
      onSubmit={onSubmit}
    >
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <DateInput label="Value Date" value={valueDate} onChange={setValueDate} />
        <DateInput label="Statement Date" value={statementDate} onChange={setStatementDate} />
      </Box>
    </Modal>
  );
};

export default AddInterestToBalanceModal;
