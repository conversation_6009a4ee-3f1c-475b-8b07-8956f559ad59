import React from 'react';
import ReactTooltip from 'react-tooltip';
import { errorHandler } from 'utils/errors';

import { getLoanPayments, markLoanPaymentAsPaid } from '~/api';
import { ThreeDotActionMenu } from '~/components/Shared';
import { updateField } from '~/reducers/payment.slice';
import { routesEnum } from '~/routes';
import { Box, FlexLayout, Text } from '~/ui';
import { showToast } from '~/ui/components/Toast';
import { formatDateString } from '~/utils/dates';
import { getReportUnit } from '~/utils/report';
import { capitalize, displayNumber2 } from '~/utils/strings';
import { getVisibleColumns } from '~/utils/tables';

function getLoanData(payment, userInfo) {
  const {
    paymentDueDate,
    isPaid,
    loan,
    isPrincipalPayment,
    bulletPayment,
    balloonPayment,
    paymentNumber,
    paymentAmount,
    totalNumberOfPayments,
  } = payment;

  const isBullet = !!bulletPayment;
  const isFloat = loan?.rateType?.type?.toLowerCase() === 'float';
  const interestPayment = isBullet ? bulletPayment.interestPayment : balloonPayment.compoundedInterest;
  // For balloons there is always only one payment
  const actualPaymentNumber = isBullet ? paymentNumber : 1;

  const {
    amount,
    currency,
    editable,
    lender,
    borrower,
    id,
    report,
    paymentFrequency,
    issueDate,
    maturityDate,
    rateType,
  } = loan;
  const numberDisplayOptions = { decimalPoint: userInfo.decimalPoint, minDig: 2, maxDig: 2 };

  return {
    id,
    paymentId: payment.id,
    isPrincipalPayment,
    isBullet,
    isFloat,
    editable,
    lender: lender?.name,
    borrower: borrower?.name,
    currency,
    paymentNumberNum: paymentNumber,
    paymentAmount: displayNumber2(paymentAmount, numberDisplayOptions),
    interestPayment: displayNumber2(interestPayment, numberDisplayOptions),
    paymentDueDate: formatDateString(paymentDueDate, userInfo.dateFormat),
    paymentDueDateNotFormatted: paymentDueDate,
    isPaid: isPaid ? 'Published' : 'Unpublished',
    loanType: `${isBullet ? 'Bullet' : 'Balloon'}`,
    paymentNumber: `${actualPaymentNumber} of ${totalNumberOfPayments}`,
    finalInterestRate: `${report?.finalInterestRate} ${getReportUnit(rateType, true)}`,
    principalAmount: displayNumber2(amount, numberDisplayOptions),
    paymentFrequency,
    issueDate: formatDateString(issueDate, userInfo.dateFormat),
    issueDateNotFormatted: issueDate,
    maturityDate: formatDateString(maturityDate, userInfo.dateFormat),
    maturityDateNotFormatted: maturityDate,
    referenceRate: rateType?.referenceRate || '-',
    rateType: capitalize(rateType?.type),
    referenceRateMaturity: rateType?.referenceRateMaturity || '-',
  };
}

export function getLoanPaymentsData(data = [], userInfo) {
  return data.map((item) => getLoanData(item, userInfo));
}

const columns = [
  {
    label: 'Lender',
    sortBy: 'lender',
    value: 'lender',
    wrapText: true,
    renderCustomCell: ({ lender, isPrincipalPayment }) => (
      <FlexLayout alignItems="center" space={2}>
        {isPrincipalPayment && (
          <>
            <Box
              bg="shakespeare"
              data-tip
              data-for="principalPayment"
              sx={{ height: '8px', width: '8px', borderRadius: 'round' }}
            />
            <ReactTooltip id="principalPayment">Principal Payment</ReactTooltip>
          </>
        )}
        <Text variant="m-spaced">{lender}</Text>
      </FlexLayout>
    ),
  },
  { label: 'Borrower', sortBy: 'borrower', value: 'borrower', wrapText: true },
  { label: 'Loan type', sortBy: 'loanType', value: 'loanType' },
  { label: 'Rate type', sortBy: 'rateType', value: 'rateType' },
  { label: 'Currency', sortBy: 'currency', value: 'currency' },
  { label: 'Interest amount', sortBy: 'paymentAmount', value: 'paymentAmount', justifyContent: 'flex-end' },
  { label: 'Interest due', sortBy: 'paymentDueDate', value: 'paymentDueDate' },
  {
    label: 'Status',
    sortBy: 'isPaid',
    value: 'isPaid',
    // important isPaid is a string for export
    renderCustomCell: ({ isPaid }) => (
      <Text color={isPaid === 'Published' ? 'deep-sapphire' : 'blaze-orange'} variant="m-spaced">
        {isPaid === 'Published' ? 'Published' : 'Unpublished'}
      </Text>
    ),
  },
  { label: 'Interest number', sortBy: 'paymentNumber', value: 'paymentNumber', justifyContent: 'flex-end' },
  { label: 'Interest frequency', sortBy: 'paymentFrequency', value: 'paymentFrequency' },
  { label: 'Issue date', sortBy: 'issueDate', value: 'issueDate' },
  { label: 'Maturity date', sortBy: 'maturityDate', value: 'maturityDate' },
  { label: 'Rate', sortBy: 'finalInterestRate', value: 'finalInterestRate', justifyContent: 'flex-end' },
  { label: 'Principal amount', sortBy: 'principalAmount', value: 'principalAmount', justifyContent: 'flex-end' },
  { label: 'Interest', sortBy: 'interestPayment', value: 'interestPayment', justifyContent: 'flex-end' },
  { label: 'Reference rate', sortBy: 'referenceRate', value: 'referenceRate' },
];

export function getLoanPaymentsColumns(visibleColumns) {
  return getVisibleColumns({ columns, visibleColumns });
}

export const renderTableActionColumn = ({
  item,
  setPayments,
  searchQuery,
  dispatch,
  setIsRepayPrincipalModalShowing,
  setIsReferenceModalShowing,
  history,
}) => {
  const options = [];

  /**
   * Only float balloons need the user to enter the reference rate for each compounding
   * period. For fixed everything is known in advance. For float bullet
   * each payment requires reference rate when marking as paid.
   */
  if (item.isFloat && !item.isBullet) {
    options.push({
      label: 'Show compounding periods',
      onClick: () => history.push(`${routesEnum.PAYMENTS}/${item.id}/compound-periods`),
    });
  } else {
    options.push({
      label: 'Show all interest',
      onClick: () => dispatch(updateField({ loanId: item.id })),
    });

    if (!item.isPrincipalPayment) {
      options.push({
        label: item.isPaid === 'Published' ? 'Unpublish' : 'Publish',
        onClick: async () => {
          if (item.isFloat && item.isPaid !== 'Published') {
            return setIsReferenceModalShowing(item);
          }
          try {
            await markLoanPaymentAsPaid(item.id, item.paymentId, { isPaid: !(item.isPaid === 'Published') });
            const loanPaymentsWithCount = await getLoanPayments({ searchQuery });
            setPayments({ loanPaymentsWithCount });
            showToast(`Payment ${item.isPaid === 'Published' ? 'unpublished' : 'published'}`);
          } catch (err) {
            errorHandler(err);
          }
        },
      });
    }
  }

  if (item.isBullet) {
    options.push({
      label: 'Repay principal amount',
      onClick: () => setIsRepayPrincipalModalShowing(item.id),
    });
  }

  return <ThreeDotActionMenu options={options} />;
};

export const loanTooltips = {
  firstCompany: 'Select the company that provides the loan.', // lender
  secondCompany: 'Select the company that receives the loan.', // borrower
  currency: 'Select the currency in which the loan is denominated.',
};
