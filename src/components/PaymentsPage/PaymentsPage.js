import _ from 'lodash';
import qs from 'query-string';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { getCashPoolPayments, getGuaranteePayments, getLoanPayments, getLoanWHTPayments } from '~/api';
import { FilterPaymentColumnsModal, ReferenceRateModal, RepayPrincipalModal } from '~/components/Modals';
import { UserInfoContext } from '~/context/user';
import { REPORT_TYPE, reportEnum } from '~/enums';
import { useCompanies } from '~/hooks';
import { paymentSelector, resetPayment, resetReportId, updatePagination } from '~/reducers/payment.slice';
import { cashPoolPaymentColumns } from '~/reducers/tableColumns/cashPoolPaymentColumns.slice';
import { guaranteePaymentColumns } from '~/reducers/tableColumns/guaranteePaymentColumns.slice';
import { loanPaymentColumns } from '~/reducers/tableColumns/loanPaymentColumns.slice';
import { withholdingTaxPaymentColumns } from '~/reducers/tableColumns/withholdingTaxPaymentsColumns.slice';
import { routesEnum } from '~/routes';
import { Button, Card, FlexLayout, PageLayout, Pagination, Tabs } from '~/ui';
import { errorHandler } from '~/utils/errors';
import { genericTabSetter } from '~/utils/tabs';

import FilterForm from './FilterForm';
import { getFiltersText, handleOnExportTableClick, setNumberOfPaymentsForPagination } from './PaymentsPage.utils';
import PaymentsTable from './PaymentsTable';

const FILTERS = 'Filters';
const payerPayeeKeys = ['lender', 'guarantor', 'borrower', 'principal', 'creditor', 'debtor'];
const filterKeys = [
  'startDate',
  'endDate',
  'currency',
  'paymentAmount',
  'rateType',
  'isPaid',
  'cashPoolId',
  ...payerPayeeKeys,
];
const queryParamKeys = [...filterKeys, 'loanId', 'guaranteeId', 'batchId', 'limit', 'offset', 'sort'];

const PaymentsPage = () => {
  const history = useHistory();
  const dispatch = useDispatch();
  const formData = useSelector(paymentSelector);
  const [isLoading, setIsLoading] = useState(true);
  const [reportType, setReportType] = useState();
  const [isColumnFilterModalShowing, setIsColumnFilterModalShowing] = useState(false);
  const [isRepayPrincipalModalShowing, setIsRepayPrincipalModalShowing] = useState(false); // false or set to loanId of payment
  const [isReferenceModalShowing, setIsReferenceModalShowing] = useState(false); // false or set to table row values of payment
  const [isFilterFormShowing, setIsFilterFormShowing] = useState(false);
  const [payments, setPayments] = useState();
  const [numberOfPayments, setNumberOfPayments] = useState();
  const [isExportButtonDisabled, setIsExportButtonDisabled] = useState(false);
  const [tabs, setTabs] = useState([]);
  const { userInfo } = useContext(UserInfoContext);
  const offset = formData.offset || 0;
  const limit = formData.limit || 10;
  const areOnlySpecificReportPaymentsShown = Boolean(formData.loanId || formData.guaranteeId || formData.batchId);
  const { features } = userInfo;

  const visibleLoanColumns = useSelector(loanPaymentColumns);
  const visibleGuaranteeColumns = useSelector(guaranteePaymentColumns);
  const visibleCashPoolColumns = useSelector(cashPoolPaymentColumns);
  const visibleWithholdingTaxColumns = useSelector(withholdingTaxPaymentColumns);

  useCompanies(); // refresh company list

  const onTabSelect = (tabName) => {
    dispatch(resetReportId());
    dispatch(updatePagination({ limit, offset: 0 }));
    dispatch(resetPayment());
    history.replace(`${routesEnum.PAYMENTS}?${REPORT_TYPE}=${tabName}`);
  };

  const getVisibleColumns = () => {
    const { reportType: reportTypeQueryString } = qs.parse(history.location.search);
    if (reportTypeQueryString === reportEnum.LOAN || reportTypeQueryString == null) {
      return Object.keys(visibleLoanColumns).filter((name) => visibleLoanColumns[name]);
    }
    if (reportTypeQueryString === reportEnum.GUARANTEE) {
      return Object.keys(visibleGuaranteeColumns).filter((name) => visibleGuaranteeColumns[name]);
    }
    if (reportTypeQueryString === reportEnum.CASH_POOL) {
      return Object.keys(visibleCashPoolColumns).filter((name) => visibleCashPoolColumns[name]);
    }
    if (reportTypeQueryString === reportEnum.WITHHOLDING_TAX) {
      return Object.keys(visibleWithholdingTaxColumns).filter((name) => visibleWithholdingTaxColumns[name]);
    }
  };

  const onPageChange = ({ selected }) => dispatch(updatePagination({ limit, offset: selected }));

  useEffect(() => {
    const tabs = [
      { isEnabled: features.loan, label: 'Loans (BETA)', value: reportEnum.LOAN },
      { isEnabled: features.guarantee, label: 'Guarantees (BETA)', value: reportEnum.GUARANTEE },
      { isEnabled: features.cashPool, label: 'Cash Pools', value: reportEnum.CASH_POOL },
      { isEnabled: features.loan, label: 'Withholding Tax', value: reportEnum.WITHHOLDING_TAX },
    ];
    genericTabSetter(setTabs, setReportType, tabs);

    return () => dispatch(resetReportId());
  }, [dispatch, features.loan, features.guarantee, features.cashPool]);

  const getQueryParams = useCallback(() => {
    const query = {};
    for (const [key, value] of Object.entries(_.pick(formData, queryParamKeys))) {
      if (value != null) query[key] = payerPayeeKeys.includes(key) ? value.id : value;
    }
    return query;
  }, [formData]);

  useEffect(() => {
    getVisibleColumns();
    const { reportType: reportTypeQueryString } = qs.parse(history.location.search);
    setReportType(reportTypeQueryString || reportEnum.LOAN);
    const searchQuery = { reportType: reportTypeQueryString, ...getQueryParams() };

    const paymentPromises = [];
    if (features.loan && (reportTypeQueryString === reportEnum.LOAN || reportTypeQueryString == null)) {
      paymentPromises[0] = getLoanPayments({ searchQuery });
    }
    if (features.guarantee && reportTypeQueryString === reportEnum.GUARANTEE) {
      paymentPromises[1] = getGuaranteePayments({ searchQuery });
    }
    if (features.cashPool && reportTypeQueryString === reportEnum.CASH_POOL) {
      paymentPromises[2] = getCashPoolPayments({ searchQuery });
    }
    if (features.loan && reportTypeQueryString === reportEnum.WITHHOLDING_TAX) {
      paymentPromises[3] = getLoanWHTPayments({ searchQuery });
    }

    setIsLoading(true);
    Promise.all(paymentPromises)
      .then(
        ([
          loanPaymentsWithCount,
          guaranteePaymentsWithCount,
          cashPoolPaymentsWithCount,
          withholdingTaxPaymentsWithCount,
        ]) => {
          const paymentsWithTotalCount = {
            loanPaymentsWithCount,
            guaranteePaymentsWithCount,
            cashPoolPaymentsWithCount,
            withholdingTaxPaymentsWithCount,
          };
          setPayments(paymentsWithTotalCount);
          setNumberOfPaymentsForPagination(reportTypeQueryString, setNumberOfPayments, paymentsWithTotalCount);
        }
      )
      .catch(errorHandler)
      .finally(() => setIsLoading(false));
  }, [formData, history.location, getQueryParams, features.cashPool, features.guarantee, features.loan]);

  return (
    <PageLayout
      rightTitleContent={
        <Button
          disabled={isExportButtonDisabled}
          loading={isExportButtonDisabled}
          iconLeft="export"
          size="s"
          text="Export"
          variant="secondary"
          onClick={() =>
            handleOnExportTableClick({
              setIsExportButtonDisabled,
              history,
              getQueryParams,
              reportType,
              getVisibleColumns,
            })
          }
        />
      }
      title="Interest"
    >
      <Card pb={3} pt={6}>
        <FlexLayout alignItems="center" justifyContent="space-between">
          <Tabs selectedTab={reportType} tabs={tabs} onTabSelect={onTabSelect} minWidth="tab-width-small" />
          <FlexLayout alignItems="center" justifyContent="space-between" space={6}>
            <Button
              iconLeft="filter"
              size="s"
              text={getFiltersText(formData, filterKeys)}
              variant={getFiltersText() !== FILTERS ? 'primary' : 'secondary'}
              onClick={() => setIsFilterFormShowing(!isFilterFormShowing)}
            />
            <Button
              iconLeft="columns"
              size="s"
              text="Columns"
              variant="secondary"
              onClick={() => setIsColumnFilterModalShowing(true)}
            />
            <Button
              iconLeft="delete"
              size="s"
              text="Clear all"
              variant={areOnlySpecificReportPaymentsShown ? 'primary' : 'secondary'}
              onClick={() => dispatch(resetPayment())}
            />
          </FlexLayout>
        </FlexLayout>

        <FilterForm isShowing={isFilterFormShowing} reportType={reportType} />
        <PaymentsTable
          isLoading={isLoading}
          payments={payments}
          setPayments={setPayments}
          searchQuery={{ ...qs.parse(history.location.search), ...getQueryParams() }}
          reportType={reportType}
          setIsRepayPrincipalModalShowing={setIsRepayPrincipalModalShowing}
          setIsReferenceModalShowing={setIsReferenceModalShowing}
        />

        <Pagination
          canNextPage={numberOfPayments - offset * limit > offset * limit}
          canPreviousPage={offset !== 0}
          pageCount={numberOfPayments / limit}
          forcePage={offset}
          onPageChange={onPageChange}
          isShowing={numberOfPayments > limit}
        />
      </Card>
      <FilterPaymentColumnsModal
        isShowing={isColumnFilterModalShowing}
        reportType={reportType}
        onHide={() => setIsColumnFilterModalShowing(false)}
      />
      <RepayPrincipalModal
        loanId={isRepayPrincipalModalShowing}
        payment={payments?.loanPaymentsWithCount?.payments?.find(
          ({ loan }) => loan.id === isRepayPrincipalModalShowing
        )}
        onHide={() => setIsRepayPrincipalModalShowing(false)}
      />
      <ReferenceRateModal payment={isReferenceModalShowing} onHide={() => setIsReferenceModalShowing(false)} />
    </PageLayout>
  );
};

export default PaymentsPage;
