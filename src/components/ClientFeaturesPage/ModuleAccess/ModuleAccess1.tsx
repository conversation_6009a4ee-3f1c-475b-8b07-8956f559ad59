import { featureNames } from 'enums';
import { Card, FlexLayout, Text } from 'ui';
import { QuestionGroup } from 'components/Shared';
import { showErrorToast } from 'ui/components/Toast';

import BoxTitle from '../BoxTitle';

type ModuleAccess1PropsType = {
  isClientView: boolean;
  options: any;
  updateFeatureAvailabilityFields: (values: string[]) => void;
  getFeatureAnswerValues: () => string[];
};

const ModuleAccess1 = ({
  isClientView,
  options,
  updateFeatureAvailabilityFields,
  getFeatureAnswerValues,
}: ModuleAccess1PropsType) => {
  const cashPoolTypeUpdate = (fields: Array<string>) => {
    if (
      fields.includes(featureNames.CASH_POOL) &&
      !fields.includes(featureNames.PHYSICAL_CASH_POOL) &&
      !fields.includes(featureNames.NOTIONAL_CASH_POOL) &&
      !fields.includes(featureNames.NORDIC_CASH_POOL)
    ) {
      return showErrorToast('At least on cash pool type must be selected.');
    }
    updateFeatureAvailabilityFields(fields);
  };

  return (
    <Card pb={6} pt={6} title={<BoxTitle title="MODULE ACCESS" />}>
      <FlexLayout flexDirection="column" space={6} disabled={isClientView}>
        <QuestionGroup
          options={options.payment}
          values={getFeatureAnswerValues()}
          updateFields={updateFeatureAvailabilityFields}
        />
        <Text variant="s-spaced" color="bali-hai" sx={{ marginTop: '-8px' }}>
          Loan and Guarantee interest calculations.
          <br />
          Note(!): this will need to be included by default for the Cash Pool subsection for cash pool subscribers
        </Text>
        <QuestionGroup options={options.cashPool} values={getFeatureAnswerValues()} updateFields={cashPoolTypeUpdate} />
        <Text variant="s-spaced" color="bali-hai" sx={{ marginTop: '-8px' }}>
          Monitoring, control and documentation of cash pooling benefit allocation, interest rates and structural
          positions based on OECD guidance. Available for Physical, Notional, and Nordic cash pools.
        </Text>
        <FlexLayout mx={6}>
          <QuestionGroup
            options={options.physicalCashPool}
            values={getFeatureAnswerValues()}
            updateFields={cashPoolTypeUpdate}
          />
          <QuestionGroup
            options={options.notionalCashPool}
            values={getFeatureAnswerValues()}
            updateFields={cashPoolTypeUpdate}
          />
          <QuestionGroup
            options={options.nordicCashPool}
            values={getFeatureAnswerValues()}
            updateFields={cashPoolTypeUpdate}
          />
        </FlexLayout>
        <QuestionGroup
          options={options.creditRating}
          values={getFeatureAnswerValues()}
          updateFields={updateFeatureAvailabilityFields}
        />
        <Text variant="s-spaced" color="bali-hai" sx={{ marginTop: '-8px' }}>
          EU ESMA registered credit rating model.
        </Text>
        <QuestionGroup
          options={options.cuftData}
          values={getFeatureAnswerValues()}
          updateFields={updateFeatureAvailabilityFields}
        />
        <Text variant="s-spaced" color="bali-hai" sx={{ marginTop: '-8px' }}>
          CUFT Agreements
        </Text>
      </FlexLayout>
    </Card>
  );
};

export default ModuleAccess1;
