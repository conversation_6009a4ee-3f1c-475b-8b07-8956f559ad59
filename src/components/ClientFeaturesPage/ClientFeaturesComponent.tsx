import { useEffect, useState, useContext } from 'react';
import { useParams } from 'react-router-dom';
import { useDispatch } from 'react-redux';

import { getClientFeatures, getSpecificClientFeatures, getClientFeaturesUsedNumbers } from 'api';
import { useAppSelector } from 'hooks';
import { Box, LoadingSpinner } from 'ui';
import { UserInfoContext } from 'context/user';
import {
  clientFeatureSelector,
  setClientFeature,
  updateFeatureAnswers,
  defaultFeatureAvailability,
} from 'reducers/clientFeature.slice';
import { showErrorToast } from 'ui/components/Toast';
import type { FeatureNameType, ClientFeatureUsedNumbersType } from 'types';
import { featureNames } from 'enums';

import { ModuleAccess1, ModuleAccess2 } from './ModuleAccess';
import DataAccess from './DataAccess';
import Limits from './Limits';

const numberTooltip = 'If checkbox is not ticked number is unlimited.';

const options = Object.freeze({
  payment: [{ label: 'Interest', value: featureNames.PAYMENT }],
  cashPool: [{ label: 'Cash Pooling', value: featureNames.CASH_POOL }],
  physicalCashPool: [{ label: 'Physical', value: featureNames.PHYSICAL_CASH_POOL }],
  notionalCashPool: [{ label: 'Notional', value: featureNames.NOTIONAL_CASH_POOL }],
  nordicCashPool: [{ label: 'Nordic', value: featureNames.NORDIC_CASH_POOL }],
  creditRating: [{ label: 'Credit Ratings', value: featureNames.CREDIT_RATING }],
  loan: [{ label: 'Loans', value: featureNames.LOAN }],
  backToBackLoan: [{ label: 'Back-to-back Loans', value: featureNames.BACK_TO_BACK_LOAN }],
  guarantee: [{ label: 'Guarantees', value: featureNames.GUARANTEE }],
  currency: [{ label: 'Currencies', value: featureNames.CURRENCY, disabled: true }],
  geographyData: [{ label: 'Geographic data', value: featureNames.GEOGRAPHY_DATA, disabled: true }],
  userNumber: [{ label: 'User Number Limit', value: featureNames.USER_NUMBER, tooltip: numberTooltip }],
  loanGuaranteeNumber: [
    { label: 'Total Analyses Number Limit', value: featureNames.LOAN_GUARANTEE_NUMBER, tooltip: numberTooltip },
  ],
  loanNumber: [{ label: 'Loan Number Limit', value: featureNames.LOAN_NUMBER, tooltip: numberTooltip }],
  guaranteeNumber: [{ label: 'Guarantee Number Limit', value: featureNames.GUARANTEE_NUMBER, tooltip: numberTooltip }],
  backToBackLoanNumber: [
    { label: 'Back-to-back Loan Number Limit', value: featureNames.BACK_TO_BACK_LOAN_NUMBER, tooltip: numberTooltip },
  ],
  creditRatingNumber: [
    { label: 'Credit Rating Number Limit', value: featureNames.CREDIT_RATING_NUMBER, tooltip: numberTooltip },
  ],
  cashPoolNumber: [{ label: 'Cash Pool Number Limit', value: featureNames.CASH_POOL_NUMBER, tooltip: numberTooltip }],
  cuftData: [{ label: 'CUFT Agreements', value: featureNames.CUFT_DATA }],
  isTemplateCashPoolBatchUpload: [
    { label: 'Is template used to upload cash pool batch', value: featureNames.IS_TEMPLATE_CASH_POOL_BATCH_UPLOAD },
  ],
});

const ClientFeaturesComponent = ({ isClientView }: { isClientView: boolean }) => {
  const { clientId } = useParams<{ clientId: string }>();
  const dispatch = useDispatch();
  const { featureAvailability } = useAppSelector(clientFeatureSelector);
  const { userInfo } = useContext(UserInfoContext);
  const [isLoading, setIsLoading] = useState(true);
  const [usedNumbers, setUsedNumbers] = useState<ClientFeatureUsedNumbersType>({} as ClientFeatureUsedNumbersType);

  const updateFeatureAvailabilityFields = (values: string[]) => {
    const updatedFeatureAvailability = featureAvailability
      ? { ...featureAvailability }
      : { ...defaultFeatureAvailability };
    for (const [key] of Object.entries(updatedFeatureAvailability)) {
      const typedKey = key as FeatureNameType;
      updatedFeatureAvailability[typedKey] = values.includes(typedKey);
    }
    dispatch(updateFeatureAnswers(updatedFeatureAvailability));
  };

  const getFeatureAnswerValues = () => {
    if (!featureAvailability) return [];

    return Object.entries(featureAvailability)
      .filter(([_, value]) => !!value)
      .map(([key]) => key);
  };

  useEffect(() => {
    /**
     * Uses either clientId from params if accessed through /clients/:clientId by superadmin or
     * user's clientId (userInfo) if access by a user through /settings
     */
    const makeGetClientFeaturesApiRequest = () => {
      const requestedClientId = Number(clientId) || userInfo.clientId;
      const getClientFeaturesApi = clientId ? getSpecificClientFeatures : getClientFeatures;
      return getClientFeaturesApi(requestedClientId);
    };

    const dbFeatureAvailability: Record<FeatureNameType, boolean> = {} as Record<FeatureNameType, boolean>;
    const dbFeaturesWithValues: Record<string, any> = {};
    const clientRequestPromise = makeGetClientFeaturesApiRequest().then((clientFeatures) => {
      for (const clientFeature of clientFeatures) {
        dbFeatureAvailability[clientFeature.feature.name] = clientFeature.isEnabled;
        if (clientFeature.values != null) dbFeaturesWithValues[clientFeature.feature.name] = clientFeature.values;
      }
      dispatch(setClientFeature({ dbFeatureAvailability, dbFeaturesWithValues }));
    });

    const usedNumbersPromise = getClientFeaturesUsedNumbers().then(setUsedNumbers);

    Promise.all([clientRequestPromise, usedNumbersPromise])
      .then(() => setIsLoading(false))
      .catch(() => showErrorToast());
  }, [clientId, userInfo.clientId, dispatch]);

  if (isLoading) return <LoadingSpinner />;

  return (
    <>
      <Box sx={{ display: 'grid', gridGap: 6, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <ModuleAccess1
          isClientView={isClientView}
          options={options}
          getFeatureAnswerValues={getFeatureAnswerValues}
          updateFeatureAvailabilityFields={updateFeatureAvailabilityFields}
        />
        <ModuleAccess2
          isClientView={isClientView}
          options={options}
          getFeatureAnswerValues={getFeatureAnswerValues}
          updateFeatureAvailabilityFields={updateFeatureAvailabilityFields}
        />
      </Box>
      <Box sx={{ display: 'grid', gridGap: 6, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <DataAccess
          isClientView={isClientView}
          options={options}
          getFeatureAnswerValues={getFeatureAnswerValues}
          updateFeatureAvailabilityFields={updateFeatureAvailabilityFields}
        />
        <Limits
          isClientView={isClientView}
          options={options}
          getFeatureAnswerValues={getFeatureAnswerValues}
          updateFeatureAvailabilityFields={updateFeatureAvailabilityFields}
          usedNumbers={usedNumbers}
        />
      </Box>
    </>
  );
};

export default ClientFeaturesComponent;
