import { DateTime, ThreeDotActionMenu } from '~/components/Shared';
import { GENERAL_NOTIFICATION_ACTIONS, reportEnum, pricingApproachEnum, pricingMethodologyEnum } from '~/enums';
import { setNotification } from '~/reducers/notifications.slice';
import { Text } from '~/ui';
import {
  getCompanyProbabilityOfDefault,
  getCompanyProbabilityOfDefaultAdj,
  getCompanyCumulativeProbabilityOfDefault,
  getCompanyRating,
  getCompanyRatingAdj,
} from '~/utils/companies';
import { formatDateString, createTenor } from '~/utils/dates';
import { checkIsImported, getReportUnit, getUpperCaseReportType } from '~/utils/report';
import { capitalize, displayNumber } from '~/utils/strings';

function _renderBoldedCell(item, value) {
  return (
    <Text
      color="deep-sapphire"
      variant={['TP Report', 'Agreement'].includes(item.name) ? 'm-spaced-medium' : 'm-spaced'}
    >
      {item[value]}
    </Text>
  );
}

export function getReportFilesColumns() {
  return [
    { label: 'Document Name', value: 'name', width: 250, renderCustomCell: (item) => _renderBoldedCell(item, 'name') },
    { label: 'Date Created', value: 'createdAt', renderCustomCell: (item) => _renderBoldedCell(item, 'createdAt') },
    { label: 'Label', value: 'label', renderCustomCell: (item) => _renderBoldedCell(item, 'label') },
    {
      label: 'Status',
      value: 'status',
      renderCustomCell: (item) => {
        return (
          <Text color={item.status === 'Final' ? 'deep-sapphire' : 'blaze-orange'} variant="m-spaced">
            {item.status}
          </Text>
        );
      },
    },
  ];
}

// just so the code is a bit clearer in ReportView
export function getReportAgreementColumns() {
  return [...getReportFilesColumns()];
}

export function renderAgreementTableActionColumn(item, onDeleteClick, onDownloadClick) {
  const options = [];

  options.push({
    label: 'Delete',
    onClick: onDeleteClick,
  });

  options.push({
    label: 'Download',
    onClick: onDownloadClick,
  });

  return <ThreeDotActionMenu options={options} />;
}

export function getReportFilesData(files, showOnlyAgreements) {
  if (showOnlyAgreements) {
    return getAgreements(files).map((agreement) => ({
      ...agreement,
      createdAt: <DateTime withTime>{agreement.createdAt}</DateTime>,
    }));
  }

  return files.map((file) => {
    const { id, isDownloading, name, label, status, createdAt } = file;

    return {
      id,
      isDownloading,
      name,
      label,
      status,
      createdAt: <DateTime withTime>{createdAt}</DateTime>,
    };
  });
}

// For the imported credit ratings
export function getCharacteristicsCreditRatingData(data, userInfo) {
  const { company, creditRating, probabilityOfDefault, closingDate } = data;

  return [
    { label: 'COMPANY', value: company?.name },
    { label: 'RATING', value: creditRating?.rating },
    { label: 'PROBABILITY OF DEFAULT', value: `${displayNumber(probabilityOfDefault, userInfo.decimalPoint)}%` },
    { label: 'CLOSING DATE', value: <DateTime>{closingDate}</DateTime> },
  ];
}

const getFullPricingApproachName = (name) => {
  if (name === pricingApproachEnum.IMPLICIT) {
    return 'Implicit support adj.';
  }

  return capitalize(name);
};

export function getCharacteristicsReportData(data, userInfo) {
  const {
    amount,
    borrower,
    ultimateBorrower,
    currency,
    guarantor,
    isPortfolio,
    issueDate,
    lender,
    ultimateLender,
    maturityDate,
    paymentFrequency,
    principal,
    rateType,
    report,
    seniority,
    terminationDate,
    pricingApproach,
    pricingMethodology,
    type,
    reportType,
    dayCount,
  } = data;

  let reportData;
  if (reportType === reportEnum.LOAN) {
    reportData = [
      { label: 'LENDER', value: `${lender?.name} (${lender?.country})` },
      { label: 'BORROWER', value: `${borrower?.name} (${borrower?.country})` },
      { label: 'ISSUE DATE', value: formatDateString(issueDate, userInfo.dateFormat) },
      { label: 'MATURITY DATE', value: formatDateString(maturityDate, userInfo.dateFormat) },
      { label: 'PRINCIPAL AMOUNT', value: `${currency} ${displayNumber(amount, userInfo.decimalPoint)}` },
      { label: 'TYPE', value: type },
      { label: 'PRINCIPAL REPAYMENT', value: 'At Maturity' },
      {
        label: 'INTEREST TYPE',
        value:
          rateType?.type === 'fixed' ? 'Fixed' : `Float ${rateType?.referenceRate} ${rateType?.referenceRateMaturity}`,
      },
      { label: 'DAY COUNT', value: dayCount },
      { label: 'INTEREST COMPOUNDING FREQUENCY', value: paymentFrequency },
      { label: 'SENIORITY', value: seniority },
    ];

    if (checkIsImported(report)) {
      reportData.push({
        label: 'BORROWER RATING',
        value: borrower?.creditRating?.rating || '-',
      });
      reportData.push({ label: 'BORROWER RATING TYPE', value: getFullPricingApproachName(pricingApproach) });
    } else {
      reportData.push({ label: 'BORROWER RATING STANDALONE', value: getCompanyRating(borrower) });
      reportData.push({
        label: 'BORROWER RATING IMPLICIT SUPPORT ADJ.',
        value: getCompanyRatingAdj(borrower, '\u2014'),
      });
    }
  }
  if (reportType === reportEnum.BACK_TO_BACK_LOAN) {
    const passThroughEntities = data.borrowers.slice(0, -1);
    reportData = [
      { label: 'PRIMARY LENDER', value: `${ultimateLender?.name} (${ultimateLender?.country})` },
      ...passThroughEntities.map((p, index) => ({
        label: `PASS THROUGH ENTITY ${index + 1}`,
        value: `${p?.name} (${p?.country})`,
      })),
      { label: 'ULTIMATE BORROWER', value: `${ultimateBorrower?.name} (${ultimateBorrower?.country})` },
      { label: 'ISSUE DATE', value: formatDateString(issueDate, userInfo.dateFormat) },
      { label: 'MATURITY DATE', value: formatDateString(maturityDate, userInfo.dateFormat) },
      { label: 'PRINCIPAL AMOUNT', value: `${currency} ${displayNumber(amount, userInfo.decimalPoint)}` },
      { label: 'TYPE', value: type },
      { label: 'PRINCIPAL REPAYMENT', value: 'At Maturity' },
      {
        label: 'INTEREST TYPE',
        value:
          rateType?.type === 'fixed' ? 'Fixed' : `Float ${rateType?.referenceRate} ${rateType?.referenceRateMaturity}`,
      },
      { label: 'DAY COUNT', value: 'ACT/365' },
      { label: 'INTEREST COMPOUNDING FREQUENCY', value: paymentFrequency },
      { label: 'SENIORITY', value: seniority },
    ];

    if (checkIsImported(report)) {
      reportData.push({
        label: 'ULTIMATE BORROWER RATING',
        value: ultimateBorrower?.creditRating?.rating || '-',
      });
      reportData.push({ label: 'ULTIMATE BORROWER RATING TYPE', value: getFullPricingApproachName(pricingApproach) });
    } else {
      reportData.push({ label: 'ULTIMATE BORROWER RATING STANDALONE', value: getCompanyRating(ultimateBorrower) });
      reportData.push({
        label: 'ULTIMATE BORROWER RATING IMPLICIT SUPPORT ADJ.',
        value: getCompanyRatingAdj(ultimateBorrower, '\u2014'),
      });
    }
  }
  if (reportType === reportEnum.GUARANTEE) {
    const tenor = displayNumber(createTenor(issueDate, terminationDate), userInfo.decimalPoint);
    const hasGuaranteeCumulativeProbabilityOfDefault = principal?.creditRating?.cumulativeProbabilityOfDefault != null;
    const isYieldExpectedLoss = pricingMethodology === pricingMethodologyEnum.YIELD_EXPECTED_LOSS_APPROACH;
    const isStandaloneApproach = pricingApproach === pricingApproachEnum.STANDALONE;
    const isImplicitSupportAdjustedApproach = pricingApproach === pricingApproachEnum.IMPLICIT;

    reportData = [
      { label: 'GUARANTOR', value: `${guarantor?.name} (${guarantor?.country})` },
      { label: 'PRINCIPAL', value: `${principal?.name} (${principal?.country})` },
      { label: 'ISSUE DATE', value: formatDateString(issueDate, userInfo.dateFormat) },
      { label: 'TERMINATION DATE', value: formatDateString(terminationDate, userInfo.dateFormat) },
      { label: 'PRINCIPAL AMOUNT', value: `${currency} ${displayNumber(amount, userInfo.decimalPoint)}` },
      { label: 'FEE PAYMENT FREQUENCY', value: paymentFrequency },
      { label: 'SENIORITY', value: seniority },
    ];

    if (checkIsImported(report)) {
      reportData.push({ label: 'GUARANTOR RATING', value: guarantor?.creditRating?.rating || '-' });
      reportData.push({ label: 'PRINCIPAL RATING', value: principal?.creditRating?.rating || '-' });
      reportData.push({ label: 'CREDIT RATING TYPE', value: getFullPricingApproachName(pricingApproach) });
    } else {
      if (isStandaloneApproach) {
        reportData.push({ label: 'GUARANTOR RATING STANDALONE', value: getCompanyRating(guarantor) });
      } else if (isImplicitSupportAdjustedApproach) {
        reportData.push({
          label: 'GUARANTOR RATING IMPLICIT SUPPORT ADJ.',
          value: getCompanyRatingAdj(guarantor, '\u2014'),
        });
      }

      if (isStandaloneApproach) {
        reportData.push({ label: 'PRINCIPAL RATING STANDALONE', value: getCompanyRating(principal) });
      } else if (isImplicitSupportAdjustedApproach) {
        reportData.push({
          label: 'PRINCIPAL RATING IMPLICIT SUPPORT ADJ.',
          value: getCompanyRatingAdj(principal, '\u2014'),
        });
      }
      if (isYieldExpectedLoss && isStandaloneApproach) {
        reportData.push({
          label: `PRINCIPAL PD (${hasGuaranteeCumulativeProbabilityOfDefault ? tenor : 1} yr.)`,
          value:
            getCompanyCumulativeProbabilityOfDefault(principal, userInfo) ??
            getCompanyProbabilityOfDefault(principal, userInfo, '\u2014'),
        });
      }
    }
    if (!checkIsImported(report) && isYieldExpectedLoss && isImplicitSupportAdjustedApproach) {
      reportData.push({
        label: `PRINCIPAL PD IMPLICIT SUPPORT ADJ. (${hasGuaranteeCumulativeProbabilityOfDefault ? tenor : 1} yr.)`,
        value:
          getCompanyCumulativeProbabilityOfDefault(principal, userInfo) ??
          getCompanyProbabilityOfDefaultAdj(principal, userInfo, '\u2014'),
      });
    }
  }
  if ((report.finalInterestRate && isPortfolio) || checkIsImported(report)) {
    reportData.push({
      label: 'FINAL INTEREST RATE (PER ANNUM)',
      value: displayNumber(report.finalInterestRate, userInfo.decimalPoint) + getReportUnit(rateType),
    });
  }

  return reportData;
}

export function getAgreements(files) {
  return files.filter((f) => f.label === 'Agreement');
}

export function getTpReports(files) {
  return files.filter((f) => f.label === 'TP Report');
}

export function isAgreementAlreadyGenerated(files) {
  return getAgreements(files).some((file) => file.isGenerated);
}

export function isTpReportAlreadyGenerated(files) {
  return getTpReports(files).some((file) => file.isGenerated);
}

/**
 * Add to portfolio should always be available for imported loans and guarantees
 * It is not available when user is editing Interest section (RatesCard).
 * Becomes available once rates are confirmed.
 * It is not available for guarantees that would not be agreed at arm's length.
 */
export function isAddToPortfolioDisabled(data, isRateFormConfirmed) {
  if (checkIsImported(data.report)) {
    return false;
  }

  if (!isRateFormConfirmed) return true;

  if (wouldNotGuaranteeBeAgreedAtArmsLength(data)) return true;

  return false;
}

export function wouldNotGuaranteeBeAgreedAtArmsLength(data) {
  const { upperBound, lowerBound } = data.report;

  return (upperBound < 0 || lowerBound > upperBound) && data.reportType === reportEnum.GUARANTEE;
}

export function isReportFilesTableShown(data) {
  if (data.isPortfolio) return true;
  return !(
    data.reportType === reportEnum.CREDIT_RATING ||
    checkIsImported(data.report) ||
    wouldNotGuaranteeBeAgreedAtArmsLength(data)
  );
}

export const getActionButtonNotificationAction = (data) => {
  if (!data.isPortfolio) return GENERAL_NOTIFICATION_ACTIONS.MOVE_TO_PORTFOLIO;
  if (data.status === 'Draft') return GENERAL_NOTIFICATION_ACTIONS.MARK_AS_FINAL;

  throw new Error('Unsupported notification');
};

export function getNotificationAction(reportType, type) {
  const upperCaseReportType = getUpperCaseReportType(reportType);

  if (type === GENERAL_NOTIFICATION_ACTIONS.DELETE) return `DELETE_${upperCaseReportType}`;
  if (type === GENERAL_NOTIFICATION_ACTIONS.PERMANENT_DELETE) return `PERMANENT_DELETE_${upperCaseReportType}`;
  if (type === GENERAL_NOTIFICATION_ACTIONS.RESTORE) return `RESTORE_${upperCaseReportType}`;
  if (type === GENERAL_NOTIFICATION_ACTIONS.MARK_AS_DRAFT) return `MARK_${upperCaseReportType}_AS_DRAFT`;
  if (type === GENERAL_NOTIFICATION_ACTIONS.MARK_AS_FINAL) return `MARK_${upperCaseReportType}_AS_FINAL`;
  if (type === GENERAL_NOTIFICATION_ACTIONS.MOVE_TO_ANALYSES) return `MOVE_${upperCaseReportType}_TO_ANALYSES`;
  if (type === GENERAL_NOTIFICATION_ACTIONS.MOVE_TO_PORTFOLIO) return `MOVE_${upperCaseReportType}_TO_PORTFOLIO`;

  throw new Error('Unsupported notification');
}

export const onNotifyAdminShowModal = (notificationType, reportType, id, dispatch) => () => {
  if (notificationType === `MOVE_${reportType}_TO_PORTFOLIO`) {
    return dispatch(
      setNotification({
        action: `MOVE_${reportType}_TO_PORTFOLIO`,
        title: 'Notify admin to move to portfolio',
        id,
      })
    );
  }

  if (notificationType === `MARK_${reportType}_AS_FINAL`) {
    return dispatch(
      setNotification({
        action: `MARK_${reportType}_AS_FINAL`,
        title: 'Notify admin to mark as final',
        id,
      })
    );
  }

  if (notificationType === `MARK_${reportType}_AS_DRAFT`) {
    return dispatch(
      setNotification({
        action: `MARK_${reportType}_AS_DRAFT`,
        title: 'Notify admin to mark as draft',
        id,
      })
    );
  }

  if (notificationType === `MOVE_${reportType}_TO_ANALYSES`) {
    return dispatch(
      setNotification({
        action: `MOVE_${reportType}_TO_ANALYSES`,
        title: 'Notify admin to move to analyses',
        id,
      })
    );
  }

  if (notificationType === `MARK_${reportType}_PAYMENT_AS_PAID`) {
    return dispatch(
      setNotification({
        action: `MARK_${reportType}_PAYMENT_AS_PAID`,
        title: 'Notify admin to mark as paid',
        id,
      })
    );
  }

  if (notificationType === `DELETE_${reportType}`) {
    return dispatch(
      setNotification({
        action: `DELETE_${reportType}`,
        title: 'Notify admin to delete',
        id,
      })
    );
  }
};

export const getConfirmModalTitle = ({ isLoan, isBackToBackLoan, formData, unit }) => {
  if (isLoan) {
    return `Generate loan agreement using an interest rate of ${formData.report?.finalInterestRate}${unit}?`;
  }
  if (isBackToBackLoan) {
    return 'Generate back-to-back loan agreements?';
  }

  return `Generate guarantee agreement using a fee of ${formData.report?.finalInterestRate}${unit}?`;
};

export const getConfirmModalAdditionalInfo = (isLoan, isBackToBackLoan) => {
  if (isBackToBackLoan) return 'Agreement will be generated for each leg of the back-to-back loan.';

  return `This can be changed by clicking Cancel and updating the ${isLoan ? 'Interest section' : 'Fee section'}`;
};
