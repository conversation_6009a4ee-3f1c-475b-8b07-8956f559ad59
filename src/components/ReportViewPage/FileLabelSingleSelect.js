import React from 'react';

import { CASH_POOL, reportEnum } from '~/enums';
import { SingleSelect } from '~/ui';
import { getOptionsFromArray } from '~/utils/arrays';

function FileLabelSingleSelect({ value, formType, width = 'fullWidth', onChange }) {
  const getLabels = () => {
    if (formType === reportEnum.CREDIT_RATING) return ['Credit Rating', 'Other'];
    if (formType === CASH_POOL) return ['Other'];
    return ['TP Report', 'Agreement', 'Credit Rating', 'Other'];
  };

  const options = getOptionsFromArray(getLabels());

  return <SingleSelect options={options} value={value} width={width} onChange={onChange} />;
}

export default FileLabelSingleSelect;
