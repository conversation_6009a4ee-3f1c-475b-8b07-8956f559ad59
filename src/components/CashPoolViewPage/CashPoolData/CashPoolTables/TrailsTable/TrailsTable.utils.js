import { formatDateString } from '~/utils/dates';
import { displayNumber2 } from '~/utils/strings';
import { getVisibleColumns } from '~/utils/tables';

function getTrailData(accountTrail, leaderBenefit, userInfo) {
  const {
    id,
    date,
    balance,
    creditInterestRate,
    debitInterestRate,
    adjustedCreditInterestRate,
    adjustedDebitInterestRate,
    adjustedCreditInterestReceived,
    adjustedDebitInterestPaid,
    netInterestBenefit,
  } = accountTrail;
  const unit = '%';

  // if adjustedCreditInterestRate doesn't exist cir and dir shouldn't be defined but they aren't set to null
  // because they are used in payments table even if the account isn't part of the pool anymore
  const [cir, dir] = adjustedCreditInterestRate != null ? [creditInterestRate, debitInterestRate] : [null, null];

  const numberDisplayOptions = { decimalPoint: userInfo.decimalPoint, defaultValue: '-', minDig: 2 };
  const rateDisplayOptions = { ...numberDisplayOptions, unit, minDig: 6, maxDig: 6 };

  return {
    id,
    date: formatDateString(date, userInfo.dateFormat),
    balance: displayNumber2(balance, numberDisplayOptions),
    creditRate: cir != null ? displayNumber2(cir * 100, rateDisplayOptions) : '-',
    debitRate: dir != null ? displayNumber2(dir * 100, rateDisplayOptions) : '-',
    creditRateAdj:
      adjustedCreditInterestRate != null ? displayNumber2(adjustedCreditInterestRate * 100, rateDisplayOptions) : '-',
    debitRateAdj:
      adjustedDebitInterestRate != null ? displayNumber2(adjustedDebitInterestRate * 100, rateDisplayOptions) : '-',
    interestReceivable: displayNumber2(adjustedCreditInterestReceived, numberDisplayOptions),
    interestPayable: displayNumber2(adjustedDebitInterestPaid, numberDisplayOptions),
    benefit: displayNumber2(netInterestBenefit, numberDisplayOptions),
    leaderBenefit: displayNumber2(leaderBenefit, numberDisplayOptions),
  };
}

export function getTrailsData(data = [], leaderBenefit, userInfo) {
  return data.map((accountTrail, index) => getTrailData(accountTrail, leaderBenefit[index].leaderBenefit, userInfo));
}

const columns = [
  { label: 'Date', sortBy: 'date', value: 'date' },
  { label: 'Balance', sortBy: 'balance', value: 'balance', justifyContent: 'flex-end' },
  { label: 'Credit Rate', sortBy: 'creditRate', value: 'creditRate', justifyContent: 'flex-end' },
  { label: 'Debit Rate', sortBy: 'debitRate', value: 'debitRate', justifyContent: 'flex-end' },
  { label: 'Credit Rate (adj.)', sortBy: 'creditRateAdj', value: 'creditRateAdj', justifyContent: 'flex-end' },
  { label: 'Debit Rate (adj.)', sortBy: 'debitRateAdj', value: 'debitRateAdj', justifyContent: 'flex-end' },
  {
    label: 'Interest Receivable',
    sortBy: 'interestReceivable',
    value: 'interestReceivable',
    justifyContent: 'flex-end',
  },
  { label: 'Interest Payable', sortBy: 'interestPayable', value: 'interestPayable', justifyContent: 'flex-end' },
  { label: 'Benefit', sortBy: 'benefit', value: 'benefit', justifyContent: 'flex-end' },
  {
    label: 'Leader Benefit',
    sortBy: 'leaderBenefit',
    value: 'leaderBenefit',
    justifyContent: 'flex-end',
  },
];

export function getVisibleTrailsColumns(visibleColumns) {
  return getVisibleColumns({ columns, visibleColumns });
}
