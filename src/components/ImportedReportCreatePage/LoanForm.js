import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  CompanySingleSelect,
  CreditRatingSingleSelect,
  CurrencySingleSelect,
  LoanTypeSingleSelect,
  PaymentFrequencySingleSelect,
  ReferenceRateMaturitySingleSelect,
  ReferenceRateSingleSelect,
  SenioritySingleSelect,
  CountrySingleSelect,
} from '~/components/Shared';
import { reportEnum, lenderOrGuarantorTypeEnum } from '~/enums';
import {
  importedReportForm,
  updateField,
  updateIsThirdParty,
  updateThirdPartyLender,
} from '~/reducers/importedReportForm.slice';
import { Box, DateInput, FlexLayout, NumberInput, RadioGroup, TextInput } from '~/ui';
import { getReportUnit } from '~/utils/report';

import { getPricingOptions } from '../PriceAndBenchmarkPage/PriceAndBenchmarkPage.utils';
import { loanTooltips } from './ImportedReportCreatePage.utils';

function LoanForm() {
  const dispatch = useDispatch();
  const {
    lender,
    borrower,
    issueDate,
    maturityDate,
    currency,
    amount,
    rateType,
    type,
    paymentFrequency,
    seniority,
    report,
    pricingApproach,
    currentCreditRating,
    isThirdParty,
  } = useSelector(importedReportForm);
  const [pricingOptions, setPricingOptions] = useState([]);

  useEffect(() => {
    if (lender.id && borrower.id) {
      const { pricingOptions, initialOption } = getPricingOptions({ lender, borrower, reportType: reportEnum.LOAN });
      const strippedPricingOptions = pricingOptions.map((option) => ({ ...option, label: option.label.split('(')[0] }));
      dispatch(updateField({ pricingApproach: pricingApproach || initialOption }));
      setPricingOptions(strippedPricingOptions);
    } else {
      setPricingOptions([]);
    }
  }, [lender, borrower, pricingApproach, dispatch]);

  return (
    <FlexLayout flexDirection="column" space={8}>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <RadioGroup
          label="Lender Type"
          value={isThirdParty ? lenderOrGuarantorTypeEnum.THIRD_PARTY : lenderOrGuarantorTypeEnum.INTERCOMPANY}
          tooltip={loanTooltips.lenderType}
          options={[
            { label: lenderOrGuarantorTypeEnum.INTERCOMPANY, value: lenderOrGuarantorTypeEnum.INTERCOMPANY },
            { label: lenderOrGuarantorTypeEnum.THIRD_PARTY, value: lenderOrGuarantorTypeEnum.THIRD_PARTY },
          ]}
          width="fullWidth"
          onChange={(lenderType) =>
            dispatch(updateIsThirdParty({ isThirdParty: lenderType === lenderOrGuarantorTypeEnum.THIRD_PARTY }))
          }
        />
        <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
          <CompanySingleSelect
            isShowing={!isThirdParty}
            label="Lender"
            excludedValues={[borrower?.id]}
            tooltip={loanTooltips.lender}
            value={lender?.id}
            width="fullWidth"
            onChange={(value) => dispatch(updateField({ lender: value }))}
          />
          <TextInput
            isShowing={isThirdParty}
            label="Lender Name"
            tooltip={loanTooltips.lenderName}
            value={lender.name}
            width="fullWidth"
            onChange={(name) => dispatch(updateThirdPartyLender({ name }))}
          />
          <CountrySingleSelect
            isShowing={isThirdParty}
            label="Lender Country"
            tooltip={loanTooltips.lenderCountry}
            value={lender.country}
            width="fullWidth"
            onChange={(country) => dispatch(updateThirdPartyLender({ country }))}
          />
        </Box>
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        <CompanySingleSelect
          label="Borrower"
          excludedValues={[lender?.id]}
          tooltip={loanTooltips.borrower}
          value={borrower?.id}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ borrower: value }))}
        />
        <CreditRatingSingleSelect
          label="Borrower Rating"
          tooltip={loanTooltips.borrowerRating}
          value={borrower?.creditRating?.newRating || currentCreditRating}
          width="fullWidth"
          onChange={(value) =>
            dispatch(
              updateField({ borrower: { ...borrower, creditRating: { ...borrower.creditRating, newRating: value } } })
            )
          }
        />
        <DateInput
          label="Issue Date"
          maxDate={new Date()}
          tooltip={loanTooltips.issueDate}
          value={issueDate}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ issueDate: value }))}
        />
        <DateInput
          label="Maturity Date"
          minDate={issueDate || new Date()}
          tooltip={loanTooltips.maturityDate}
          value={maturityDate}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ maturityDate: value }))}
        />
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        <CurrencySingleSelect
          tooltip={loanTooltips.currency}
          value={currency}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ currency: value }))}
        />
        <NumberInput
          allowNegatives={false}
          label="Principal Amount"
          tooltip={loanTooltips.amount}
          value={amount}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ amount: value }))}
        />
        <PaymentFrequencySingleSelect
          currency={currency}
          issueDate={issueDate}
          endDate={maturityDate}
          rateType={rateType?.type}
          tooltip={loanTooltips.paymentFrequency}
          value={paymentFrequency}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ paymentFrequency: value }))}
        />
        <SenioritySingleSelect
          tooltip={loanTooltips.seniority}
          value={seniority}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ seniority: value }))}
        />
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <RadioGroup
          label="Interest Type"
          options={[
            { label: 'Fixed', value: 'fixed' },
            { label: 'Float', value: 'float' },
          ]}
          tooltip={loanTooltips.rateType}
          value={rateType?.type}
          width="fullWidth"
          onChange={(value) => {
            const updatedField =
              value === 'float'
                ? { rateType: { type: value, referenceRate: null, referenceRateMaturity: null } }
                : { rateType: { type: value } };
            dispatch(updateField(updatedField));
          }}
        />
        <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
          <ReferenceRateSingleSelect
            isShowing={rateType?.type === 'float'}
            value={rateType?.referenceRate}
            width="fullWidth"
            onChange={(value) => dispatch(updateField({ rateType: { ...rateType, referenceRate: value } }))}
            issueDate={issueDate}
          />
          <ReferenceRateMaturitySingleSelect
            isShowing={rateType?.type === 'float'}
            value={rateType?.referenceRateMaturity}
            width="fullWidth"
            onChange={(value) => dispatch(updateField({ rateType: { ...rateType, referenceRateMaturity: value } }))}
          />
        </Box>
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        <NumberInput
          inputType="float"
          label="Interest Rate"
          tooltip={loanTooltips.finalInterestRate}
          unit={getReportUnit(rateType)}
          value={report?.finalInterestRate}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ report: { ...report, finalInterestRate: value } }))}
        />
        <LoanTypeSingleSelect
          tooltip={loanTooltips.type}
          value={type}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ type: value }))}
        />
      </Box>
      <Box>
        <RadioGroup
          isShowing={pricingOptions.length}
          label="Pricing Approach"
          value={pricingApproach}
          options={pricingOptions}
          width="fullWidth"
          onChange={(pricingApproach) => dispatch(updateField({ pricingApproach }))}
        />
      </Box>
    </FlexLayout>
  );
}

export default LoanForm;
