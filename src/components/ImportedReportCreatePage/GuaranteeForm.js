import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  CompanySingleSelect,
  CreditRatingSingleSelect,
  CurrencySingleSelect,
  PaymentFrequencySingleSelect,
  SenioritySingleSelect,
  CountrySingleSelect,
} from '~/components/Shared';
import { reportEnum, lenderOrGuarantorTypeEnum } from '~/enums';
import {
  importedReportForm,
  updateField,
  updateIsThirdParty,
  updateThirdPartyLender,
} from '~/reducers/importedReportForm.slice';
import { Box, DateInput, FlexLayout, NumberInput, RadioGroup, TextInput } from '~/ui';

import { getPricingOptions } from '../PriceAndBenchmarkPage/PriceAndBenchmarkPage.utils';
import { guaranteeTooltips } from './ImportedReportCreatePage.utils';

function GuaranteeForm() {
  const dispatch = useDispatch();
  const {
    guarantor,
    principal,
    issueDate,
    terminationDate,
    currency,
    amount,
    paymentFrequency,
    seniority,
    report,
    pricingApproach,
    currentGuarantorRating,
    currentPrincipalRating,
    isThirdParty,
  } = useSelector(importedReportForm);
  const [pricingOptions, setPricingOptions] = useState([]);

  useEffect(() => {
    if (guarantor.id && principal.id) {
      const { pricingOptions, initialOption } = getPricingOptions({
        guarantor,
        principal,
        reportType: reportEnum.GUARANTEE,
      });
      const strippedPricingOptions = pricingOptions.map((option) => ({ ...option, label: option.label.split('(')[0] }));
      dispatch(updateField({ pricingApproach: pricingApproach || initialOption }));
      setPricingOptions(strippedPricingOptions);
    } else {
      setPricingOptions([]);
    }
  }, [guarantor, principal, pricingApproach, dispatch]);

  return (
    <FlexLayout flexDirection="column" space={8}>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
        <RadioGroup
          label="Guarantor Type"
          value={isThirdParty ? lenderOrGuarantorTypeEnum.THIRD_PARTY : lenderOrGuarantorTypeEnum.INTERCOMPANY}
          tooltip={guaranteeTooltips.guarantorType}
          options={[
            { label: lenderOrGuarantorTypeEnum.INTERCOMPANY, value: lenderOrGuarantorTypeEnum.INTERCOMPANY },
            { label: lenderOrGuarantorTypeEnum.THIRD_PARTY, value: lenderOrGuarantorTypeEnum.THIRD_PARTY },
          ]}
          width="fullWidth"
          onChange={(lenderType) =>
            dispatch(updateIsThirdParty({ isThirdParty: lenderType === lenderOrGuarantorTypeEnum.THIRD_PARTY }))
          }
        />
        <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(2, 1fr)' }}>
          <CompanySingleSelect
            isShowing={!isThirdParty}
            label="Guarantor"
            excludedValues={[principal?.id]}
            tooltip={guaranteeTooltips.guarantor}
            value={guarantor?.id}
            width="fullWidth"
            onChange={(value) => dispatch(updateField({ guarantor: value }))}
          />
          <CreditRatingSingleSelect
            isShowing={!isThirdParty}
            label="Guarantor Rating"
            tooltip={guaranteeTooltips.guarantorRating}
            value={guarantor?.creditRating?.newRating || currentGuarantorRating}
            width="fullWidth"
            onChange={(value) =>
              dispatch(
                updateField({
                  guarantor: { ...guarantor, creditRating: { ...guarantor.creditRating, newRating: value } },
                })
              )
            }
          />
          <TextInput
            isShowing={isThirdParty}
            label="Guarantor Name"
            value={guarantor.name}
            tooltip={guaranteeTooltips.guarantorName}
            width="fullWidth"
            onChange={(name) => dispatch(updateThirdPartyLender({ name }))}
          />
          <CountrySingleSelect
            isShowing={isThirdParty}
            label="Guarantor Country"
            value={guarantor.country}
            tooltip={guaranteeTooltips.guarantorCountry}
            width="fullWidth"
            onChange={(country) => dispatch(updateThirdPartyLender({ country }))}
          />
        </Box>
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        <CompanySingleSelect
          label="Principal"
          excludedValues={[guarantor?.id]}
          tooltip={guaranteeTooltips.principal}
          value={principal?.id}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ principal: value }))}
        />
        <CreditRatingSingleSelect
          label="Principal Rating"
          tooltip={guaranteeTooltips.principalRating}
          value={principal?.creditRating?.newRating || currentPrincipalRating}
          width="fullWidth"
          onChange={(value) =>
            dispatch(
              updateField({
                principal: { ...principal, creditRating: { ...principal.creditRating, newRating: value } },
              })
            )
          }
        />
        <DateInput
          label="Issue Date"
          maxDate={new Date()}
          tooltip={guaranteeTooltips.issueDate}
          value={issueDate}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ issueDate: value }))}
        />
        <DateInput
          label="Termination Date"
          minDate={issueDate || new Date()}
          tooltip={guaranteeTooltips.terminationDate}
          value={terminationDate}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ terminationDate: value }))}
        />
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        <CurrencySingleSelect
          tooltip={guaranteeTooltips.currency}
          value={currency}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ currency: value }))}
        />
        <NumberInput
          allowNegatives={false}
          label="Amount"
          tooltip={guaranteeTooltips.amount}
          value={amount}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ amount: value }))}
        />
        <PaymentFrequencySingleSelect
          label="Payment Frequency"
          issueDate={issueDate}
          endDate={terminationDate}
          rateType="fixed"
          tooltip={guaranteeTooltips.paymentFrequency}
          value={paymentFrequency}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ paymentFrequency: value }))}
        />
        <SenioritySingleSelect
          isShowing={!isThirdParty}
          tooltip={guaranteeTooltips.seniority}
          value={seniority}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ seniority: value }))}
        />
      </Box>
      <Box sx={{ display: 'grid', gridGap: 8, gridTemplateColumns: 'repeat(4, 1fr)' }}>
        <NumberInput
          inputType="float"
          label="Guarantee Fee"
          tooltip={guaranteeTooltips.finalInterestRate}
          unit="%"
          value={report?.finalInterestRate}
          width="fullWidth"
          onChange={(value) => dispatch(updateField({ report: { ...report, finalInterestRate: value } }))}
        />
      </Box>
      <Box>
        <RadioGroup
          isShowing={pricingOptions.length}
          label="Pricing Approach"
          value={pricingApproach}
          options={pricingOptions}
          width="fullWidth"
          onChange={(pricingApproach) => dispatch(updateField({ pricingApproach }))}
        />
      </Box>
    </FlexLayout>
  );
}

export default GuaranteeForm;
