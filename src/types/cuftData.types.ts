import { TrancheAssetClassEnum, TrancheTypeEnum } from 'enums/cuftData';
import { CreditRatingValueType } from './creditRating.types';
import { DbAttributesType, CountryValueType, CurrencyValueType } from './various.types';

export type TrancheAssetClassType = keyof typeof TrancheAssetClassEnum;
export type TrancheTypeType = keyof typeof TrancheTypeEnum;

export type CuftDataType = {
  cuftDataFileId: number;
  filingCompanyName: string;
  cuftBorrowerName: string;
  cuftBorrowerCountry: string; // these are cuft country codes, instead of full country names
  primarySic: number;
  allCurrencies: string;
  moodyPrincipalObligorCreditRating: string;
  spyPrincipalObligorCreditRating: string;
  cuftTrancheExecutionDate: Date;
  cuftTrancheMaturityDate: Date;
  cuftTrancheTenor: number;
  cuftTrancheAssetClass: TrancheAssetClassType;
  cuftTrancheType: TrancheTypeType;
  cuftTranchePrimaryReferenceRate: string;
  trancheOrderID: string;
  reviewCtrlrID: string;
  exhibitLink: string;
  deliveryDate: Date;
  createdAt: Date;
  updatedAt: Date;
};

export type DbCuftDataType = CuftDataType & { id: number };
export type DbCuftDataTypeWithCreditRatingsType = DbCuftDataType & {
  creditRatings: { id: number; creditRatingName: CreditRatingValueType; creditRatingValue: number };
};

export type CreateCuftDataFromCSVType = { cuftDataId: number; name: string };
export type DbCuftDataFromCSVType = CreateCuftDataFromCSVType & DbAttributesType;

export type CreateCuftDataCurrenciesType = { cuftDataId: number; currency: string };
export type DbCuftDataCurrenciesType = CreateCuftDataCurrenciesType & DbAttributesType;

export type CreateCuftDataCreditRatingsType = {
  cuftDataId: number;
  creditRatingName: CreditRatingValueType;
  creditRatingValue: number;
};
export type DbCuftDataCreditRatingsType = CreateCuftDataCreditRatingsType & DbAttributesType;

export type CuftDataSummaryStatisticsType = {
  maximum: number;
  upperQuartile: number;
  median: number;
  lowerQuartile: number;
  minimum: number;
  numberOfObservations: number;
};

export type CuftDataSaveSearchBodyType = {
  issueDate: Date | null;
  numberOfMonthsBeforeIssueDate: number;
  tenor: number | null;
  numberOfYearsBeforeAndAfterTenor: number;
  creditRatings: Array<CreditRatingValueType>;
  currencies: Array<CurrencyValueType>;
  countries: Array<CountryValueType>;
  trancheAssetClasses: Array<TrancheAssetClassType>;
};

export type CuftDataSavedSearchType = {
  id: number;
  userId: number;
  name: string;
  search: CuftDataSaveSearchBodyType;
  createdAt: Date;
  updatedAt: Date;
};

export type CuftDataAvailableFiltersType = {
  cuftDataAvailableBorrowerCountries: Array<CountryValueType>;
  cuftDataAvailableTrancheAssetClasses: Array<TrancheAssetClassType>;
  cuftDataAvailableCurrencies: Array<CurrencyValueType>;
};
