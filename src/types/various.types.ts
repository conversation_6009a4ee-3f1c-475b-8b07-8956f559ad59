import { currenciesArray } from 'components/Shared/CurrencySingleSelect';
import { palette } from 'ui/theme/colors';

export type DateFormatType = 'YYYY-MM-DD' | 'DD-MM-YYYY' | 'MM-DD-YYYY';

export type DecimalPointType = 'dot' | 'comma';

export type SingleSelectWidthType = 's' | 'm' | 'l' | 'fullWidth';

export type HeightType = 'input-height' | 'input-height-text' | 'input-height-small';

export type GeographyDataType = 'basic' | 'full';

export type ReportTypeType = 'loan' | 'back-to-back loan' | 'guarantee' | 'credit rating';

export type CurrencyVariantType = 'default' | 'short' | 'extended' | 'extendedShort';

export type CurrencyRegions =
  | 'Global Core'
  | 'World'
  | 'Asia'
  | 'W. Europe'
  | 'E. Europe'
  | 'Oceania'
  | 'Latin America'
  | 'Middle East & Africa';

export type CurrencyValueType = (typeof currenciesArray)[number];

export type ColorType = keyof typeof palette;

export type CountryValueType =
  | "Côte d'Ivoire, Republic of"
  | 'Afghanistan'
  | 'Åland Islands'
  | 'Albania'
  | 'Algeria'
  | 'American Samoa'
  | 'Andorra'
  | 'Angola'
  | 'Anguilla'
  | 'Antarctica'
  | 'Antigua and Barbuda'
  | 'Argentina'
  | 'Armenia'
  | 'Aruba'
  | 'Australia'
  | 'Austria'
  | 'Azerbaijan'
  | 'Bahamas'
  | 'Bahrain'
  | 'Bangladesh'
  | 'Barbados'
  | 'Belarus'
  | 'Belgium'
  | 'Belize'
  | 'Benin'
  | 'Bermuda'
  | 'Bhutan'
  | 'Bolivia'
  | 'Bonaire, Sint Eustatius and Saba'
  | 'Bosnia and Herz.'
  | 'Botswana'
  | 'Bouvet Island'
  | 'Brazil'
  | 'British Indian Ocean Territory'
  | 'Brunei'
  | 'Bulgaria'
  | 'Burkina Faso'
  | 'Burundi'
  | 'Cambodia'
  | 'Cameroon'
  | 'Canada'
  | 'Cape Verde'
  | 'Cayman Islands'
  | 'Central African Rep.'
  | 'Chad'
  | 'Chile'
  | 'China'
  | 'Christmas Island'
  | 'Cocos (Keeling) Islands'
  | 'Colombia'
  | 'Comoros'
  | 'Congo'
  | 'Cook Islands'
  | 'Costa Rica'
  | 'Croatia'
  | 'Cuba'
  | 'Curaçao'
  | 'Cyprus'
  | 'Czech Republic'
  | 'Dem. Rep. Congo'
  | 'Denmark'
  | 'Djibouti'
  | 'Dominica'
  | 'Dominican Rep.'
  | 'Ecuador'
  | 'Egypt'
  | 'El Salvador'
  | 'Eq. Guinea'
  | 'Eritrea'
  | 'Estonia'
  | 'Ethiopia'
  | 'Falkland Is.'
  | 'Faroe Islands'
  | 'Fiji'
  | 'Finland'
  | 'Fr. S. Antarctic Lands'
  | 'France'
  | 'French Guiana'
  | 'French Polynesia'
  | 'Gabon'
  | 'Gambia'
  | 'Georgia'
  | 'Germany'
  | 'Ghana'
  | 'Gibraltar'
  | 'Greece'
  | 'Greenland'
  | 'Grenada'
  | 'Guadeloupe'
  | 'Guam'
  | 'Guatemala'
  | 'Guernsey'
  | 'Guinea'
  | 'Guinea-Bissau'
  | 'Guyana'
  | 'Haiti'
  | 'Heard Island and McDonald Islands'
  | 'Holy See (Vatican City)'
  | 'Honduras'
  | 'Hong Kong'
  | 'Hungary'
  | 'Iceland'
  | 'India'
  | 'Indonesia'
  | 'Iran'
  | 'Iraq'
  | 'Ireland'
  | 'Isle of Man'
  | 'Israel'
  | 'Italy'
  | 'Jamaica'
  | 'Japan'
  | 'Jersey'
  | 'Jordan'
  | 'Kazakhstan'
  | 'Kenya'
  | 'Kiribati'
  | 'Kosovo'
  | 'Kuwait'
  | 'Kyrgyzstan'
  | 'Laos'
  | 'Latvia'
  | 'Lebanon'
  | 'Lesotho'
  | 'Liberia'
  | 'Libya'
  | 'Liechtenstein'
  | 'Lithuania'
  | 'Luxembourg'
  | 'Macao'
  | 'Macedonia'
  | 'Madagascar'
  | 'Malawi'
  | 'Malaysia'
  | 'Maldives'
  | 'Mali'
  | 'Malta'
  | 'Marshall Islands'
  | 'Martinique'
  | 'Mauritania'
  | 'Mauritius'
  | 'Mayotte'
  | 'Mexico'
  | 'Micronesia, Federated States of'
  | 'Moldova'
  | 'Monaco'
  | 'Mongolia'
  | 'Montenegro'
  | 'Montserrat'
  | 'Morocco'
  | 'Mozambique'
  | 'Myanmar'
  | 'N. Cyprus'
  | 'Namibia'
  | 'Nauru'
  | 'Nepal'
  | 'Netherlands'
  | 'New Caledonia'
  | 'New Zealand'
  | 'Nicaragua'
  | 'Niger'
  | 'Nigeria'
  | 'Niue'
  | 'Norfolk Island'
  | 'North Korea'
  | 'Northern Mariana Islands'
  | 'Norway'
  | 'Oman'
  | 'Pakistan'
  | 'Palau'
  | 'Palestine'
  | 'Panama'
  | 'Papua New Guinea'
  | 'Paraguay'
  | 'Peru'
  | 'Philippines'
  | 'Pitcairn'
  | 'Poland'
  | 'Portugal'
  | 'Puerto Rico'
  | 'Qatar'
  | 'Réunion'
  | 'Romania'
  | 'Russia'
  | 'Rwanda'
  | 'S. Sudan'
  | 'Saint Barthélemy'
  | 'Saint Helena, Ascension and Tristan da Cunha'
  | 'Saint Kitts and Nevis'
  | 'Saint Lucia'
  | 'Saint Martin'
  | 'Saint Pierre and Miquelon'
  | 'Saint Vincent and the Grenadines'
  | 'Samoa'
  | 'San Marino'
  | 'Sao Tome and Principe'
  | 'Saudi Arabia'
  | 'Senegal'
  | 'Serbia'
  | 'Seychelles'
  | 'Sierra Leone'
  | 'Singapore'
  | 'Sint Maarten (Dutch part)'
  | 'Slovakia'
  | 'Slovenia'
  | 'Solomon Is.'
  | 'Somalia'
  | 'Somaliland'
  | 'South Africa'
  | 'South Georgia and South Sandwich Islands'
  | 'South Korea'
  | 'Spain'
  | 'Sri Lanka'
  | 'Sudan'
  | 'Suriname'
  | 'Swaziland'
  | 'Sweden'
  | 'Switzerland'
  | 'Syria'
  | 'Taiwan'
  | 'Tajikistan'
  | 'Tanzania'
  | 'Thailand'
  | 'Timor-Leste'
  | 'Togo'
  | 'Tokelau'
  | 'Tonga'
  | 'Trinidad and Tobago'
  | 'Tunisia'
  | 'Turkey'
  | 'Turkmenistan'
  | 'Turks and Caicos Islands'
  | 'Tuvalu'
  | 'Uganda'
  | 'Ukraine'
  | 'United Arab Emirates'
  | 'United Kingdom'
  | 'United States Minor Outlying Islands'
  | 'United States of America'
  | 'Uruguay'
  | 'Uzbekistan'
  | 'Vanuatu'
  | 'Venezuela'
  | 'Vietnam'
  | 'Virgin Islands, British'
  | 'Virgin Islands, U.S.'
  | 'W. Sahara'
  | 'Wallis and Futuna'
  | 'Yemen'
  | 'Zambia'
  | 'Zimbabwe';

export type CreatableSelectOptionType = {
  label: string;
  value: string | number;
  __isNew__?: boolean;
  options?: CreatableSelectOptionType;
};

export type TableColumnType<ValueType> = {
  value: ValueType;
  label: string | React.ReactNode;
  sortBy: string;
  justifyContent?: 'flex-end';
  sortArray?: any[];
  sortType?: 'normal' | 'array';
  width?: number;
  renderCustomCell?: Function;
  wrapText?: boolean;
  accessor?: string;
  sortingFn?: (rowA: any, rowB: any, id: string, desc: boolean) => void;
};

export type DbAttributesType = {
  id: number;
  createdAt: Date;
  updatedAt: Date;
};

export type TabType<ValueType> = { isEnabled: boolean; label: string; value: ValueType };

export type DropdownOptionsType<ValueType extends string> = Array<{
  label: string;
  value: ValueType;
}>;
