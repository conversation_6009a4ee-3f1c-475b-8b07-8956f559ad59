const request = require('supertest');
const app = require('../../app').default;
const { authSetup, rateLimiterSetup } = require('../setup');

// Mock the repositories
jest.mock('../../repositories', () => ({
  cashPoolRepository: {
    getCashPool: jest.fn(),
  },
  cashPoolParticipantAccountRepository: {
    getCashPoolTrails: jest.fn(),
  },
}));

// Mock the auth middleware, rate limiter, and features middleware
jest.mock('../../middlewares/authMethodMiddleware');
jest.mock('../../middlewares/rateLimiter');
jest.mock('../../middlewares/features', () => () => (req, res, next) => next());

const { cashPoolRepository, cashPoolParticipantAccountRepository } = require('../../repositories');

describe('Structural Positions Controller', () => {
  beforeAll(() => {
    rateLimiterSetup.basicRateLimiterSetup();
    authSetup.superadminAuthSetup();
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /cash-pool/:cashPoolId/trail/structural-positions', () => {
    const cashPoolId = 123;
    const mockCashPool = { id: cashPoolId, clientId: 1 };

    beforeEach(() => {
      cashPoolRepository.getCashPool.mockResolvedValue(mockCashPool);
    });

    test('should detect structural positions without materiality threshold', async () => {
      const mockTrails = [
        {
          participant: { company: { name: 'Test Company' } },
          accountTrails: [
            { balance: 100000, date: '2023-01-01' },
            { balance: 150000, date: '2023-02-01' },
            { balance: 120000, date: '2023-03-01' },
            { balance: 110000, date: '2023-04-01' },
            { balance: 130000, date: '2023-05-01' },
            { balance: 140000, date: '2023-06-01' },
            { balance: 125000, date: '2023-07-01' },
            { balance: 135000, date: '2023-08-01' },
            { balance: 145000, date: '2023-09-01' },
            { balance: 155000, date: '2023-10-01' },
          ],
        },
      ];

      cashPoolParticipantAccountRepository.getCashPoolTrails.mockResolvedValue(mockTrails);

      const response = await request(app)
        .post(`/api/cash-pool/${cashPoolId}/trail/structural-positions`)
        .send({
          structuralThreshold: 9,
          materialityThreshold: 0,
          companyIds: [1],
          startDate: '2023-01-01',
          endDate: '2023-12-31',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(1);
      expect(response.body[0]).toMatchObject({
        company: 'Test Company',
        position: 'Credit',
        positionStatus: 'Ongoing',
      });
    });

    test('should break structural position when balance enters materiality threshold range', async () => {
      const mockTrails = [
        {
          participant: { company: { name: 'Test Company' } },
          accountTrails: [
            { balance: 200000, date: '2023-01-01' },
            { balance: 180000, date: '2023-02-01' },
            { balance: 160000, date: '2023-03-01' },
            { balance: 140000, date: '2023-04-01' },
            { balance: 120000, date: '2023-05-01' },
            { balance: 50000, date: '2023-06-01' }, // Within materiality threshold (100k)
            { balance: 30000, date: '2023-07-01' }, // Still within threshold
            { balance: 150000, date: '2023-08-01' }, // Back outside threshold
            { balance: 170000, date: '2023-09-01' },
            { balance: 180000, date: '2023-10-01' },
          ],
        },
      ];

      cashPoolParticipantAccountRepository.getCashPoolTrails.mockResolvedValue(mockTrails);

      const response = await request(app)
        .post(`/api/cash-pool/${cashPoolId}/trail/structural-positions`)
        .send({
          structuralThreshold: 4, // 4 months threshold
          materialityThreshold: 100000, // 100k materiality threshold
          companyIds: [1],
          startDate: '2023-01-01',
          endDate: '2023-12-31',
        });

      expect(response.status).toBe(200);
      // Should have one ended structural position (Jan-May) and no ongoing position
      // because the position was broken by entering the materiality range
      expect(response.body).toHaveLength(1);
      expect(response.body[0]).toMatchObject({
        company: 'Test Company',
        position: 'Credit',
        positionStatus: 'Ended',
      });
    });

    test('should not detect structural position when balance stays within materiality threshold', async () => {
      const mockTrails = [
        {
          participant: { company: { name: 'Test Company' } },
          accountTrails: [
            { balance: 50000, date: '2023-01-01' },
            { balance: 60000, date: '2023-02-01' },
            { balance: 70000, date: '2023-03-01' },
            { balance: 80000, date: '2023-04-01' },
            { balance: 90000, date: '2023-05-01' },
            { balance: 85000, date: '2023-06-01' },
            { balance: 75000, date: '2023-07-01' },
            { balance: 65000, date: '2023-08-01' },
            { balance: 55000, date: '2023-09-01' },
            { balance: 45000, date: '2023-10-01' },
          ],
        },
      ];

      cashPoolParticipantAccountRepository.getCashPoolTrails.mockResolvedValue(mockTrails);

      const response = await request(app)
        .post(`/api/cash-pool/${cashPoolId}/trail/structural-positions`)
        .send({
          structuralThreshold: 9,
          materialityThreshold: 100000, // All balances are within this threshold
          companyIds: [1],
          startDate: '2023-01-01',
          endDate: '2023-12-31',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(0); // No structural positions detected
    });

    test('should handle negative balances with materiality threshold', async () => {
      const mockTrails = [
        {
          participant: { company: { name: 'Test Company' } },
          accountTrails: [
            { balance: -200000, date: '2023-01-01' },
            { balance: -180000, date: '2023-02-01' },
            { balance: -160000, date: '2023-03-01' },
            { balance: -140000, date: '2023-04-01' },
            { balance: -120000, date: '2023-05-01' },
            { balance: -50000, date: '2023-06-01' }, // Within materiality threshold
            { balance: -30000, date: '2023-07-01' },
            { balance: -150000, date: '2023-08-01' }, // Back outside threshold
            { balance: -170000, date: '2023-09-01' },
            { balance: -180000, date: '2023-10-01' },
          ],
        },
      ];

      cashPoolParticipantAccountRepository.getCashPoolTrails.mockResolvedValue(mockTrails);

      const response = await request(app)
        .post(`/api/cash-pool/${cashPoolId}/trail/structural-positions`)
        .send({
          structuralThreshold: 4,
          materialityThreshold: 100000,
          companyIds: [1],
          startDate: '2023-01-01',
          endDate: '2023-12-31',
        });

      expect(response.status).toBe(200);
      expect(response.body).toHaveLength(1);
      expect(response.body[0]).toMatchObject({
        company: 'Test Company',
        position: 'Debit',
        positionStatus: 'Ended',
      });
    });
  });
});
